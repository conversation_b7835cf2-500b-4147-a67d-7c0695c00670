{"name": "niuniu-mcp", "version": "1.0.0", "description": "在线牛牛游戏 - 为线下朋友聚会提供便捷的发牌和游戏管理", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["<PERSON><PERSON><PERSON>", "card-game", "multiplayer", "nodejs", "socket.io"], "author": "", "license": "ISC", "dependencies": {"express": "^5.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"node-fetch": "^3.3.2", "nodemon": "^3.1.10", "socket.io-client": "^4.8.1"}}