const express = require('express');
const router = express.Router();

/**
 * API路由模块
 * 提供RESTful API接口
 */

// 全局变量，用于存储SocketHandler实例
let socketHandler = null;

// 设置SocketHandler实例
function setSocketHandler(handler) {
    socketHandler = handler;
}

// 中间件：检查SocketHandler是否可用
function requireSocketHandler(req, res, next) {
    if (!socketHandler) {
        return res.status(503).json({
            success: false,
            message: '服务暂时不可用'
        });
    }
    next();
}

// 获取服务器状态
router.get('/status', (req, res) => {
    try {
        const stats = socketHandler ? socketHandler.getStats() : null;
        res.json({
            success: true,
            data: {
                status: 'running',
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString(),
                stats: stats
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取状态失败',
            error: error.message
        });
    }
});

// 获取房间统计信息
router.get('/rooms/stats', requireSocketHandler, (req, res) => {
    try {
        const stats = socketHandler.getStats();
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取房间统计失败',
            error: error.message
        });
    }
});

// 获取所有房间列表（管理用）
router.get('/rooms', requireSocketHandler, (req, res) => {
    try {
        const rooms = socketHandler.roomManager.getAllRooms();
        res.json({
            success: true,
            data: rooms
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取房间列表失败',
            error: error.message
        });
    }
});

// 获取特定房间信息
router.get('/rooms/:roomCode', requireSocketHandler, (req, res) => {
    try {
        const { roomCode } = req.params;
        const room = socketHandler.roomManager.getRoom(roomCode);
        
        if (!room) {
            return res.status(404).json({
                success: false,
                message: '房间不存在'
            });
        }
        
        res.json({
            success: true,
            data: room.getDetailedInfo()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取房间信息失败',
            error: error.message
        });
    }
});

// 验证房间码是否存在
router.post('/rooms/validate', requireSocketHandler, (req, res) => {
    try {
        const { roomCode } = req.body;
        
        if (!roomCode) {
            return res.status(400).json({
                success: false,
                message: '房间码不能为空'
            });
        }
        
        const room = socketHandler.roomManager.getRoom(roomCode);
        const exists = !!room;
        
        res.json({
            success: true,
            data: {
                exists: exists,
                roomCode: roomCode,
                canJoin: exists && room.status === 'waiting' && room.players.size < room.maxPlayers
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '验证房间码失败',
            error: error.message
        });
    }
});

// 强制删除房间（管理用）
router.delete('/rooms/:roomCode', requireSocketHandler, (req, res) => {
    try {
        const { roomCode } = req.params;
        const success = socketHandler.roomManager.forceDeleteRoom(roomCode);
        
        if (success) {
            res.json({
                success: true,
                message: '房间删除成功'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '房间不存在'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '删除房间失败',
            error: error.message
        });
    }
});

// 清理过期房间
router.post('/rooms/cleanup', requireSocketHandler, (req, res) => {
    try {
        const { timeoutMinutes = 60 } = req.body;
        const cleanedCount = socketHandler.roomManager.cleanupExpiredRooms(timeoutMinutes);
        
        res.json({
            success: true,
            data: {
                cleanedCount: cleanedCount,
                message: `清理了 ${cleanedCount} 个过期房间`
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '清理房间失败',
            error: error.message
        });
    }
});

// 获取游戏规则说明
router.get('/game/rules', (req, res) => {
    res.json({
        success: true,
        data: {
            title: '牛牛游戏规则',
            description: '经典的牛牛扑克游戏',
            rules: [
                '每人发5张牌',
                '前3张牌组合成10的倍数为"有牛"',
                '后2张牌点数相加的个位数为"牛数"',
                '特殊牌型：牛牛、五小牛、炸弹、五花牛等',
                'A=1点，2-10=面值，J/Q/K=10点'
            ],
            cardTypes: [
                { name: '五小牛', points: 50, description: '5张牌都小于5且总和≤10' },
                { name: '炸弹', points: 40, description: '4张相同点数的牌' },
                { name: '五花牛', points: 30, description: '5张牌都是J、Q、K' },
                { name: '牛牛', points: 20, description: '前3张和后2张都是10的倍数' },
                { name: '牛1-牛9', points: '1-9', description: '前3张是10的倍数，后2张点数' },
                { name: '没牛', points: 0, description: '无法组成10的倍数' }
            ]
        }
    });
});

// 健康检查
router.get('/health', (req, res) => {
    res.json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('API错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
});

module.exports = {
    router,
    setSocketHandler
};
