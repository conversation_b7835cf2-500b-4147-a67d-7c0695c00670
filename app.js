const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const SocketHandler = require('./src/controllers/SocketHandler');
const { router: apiRouter, setSocketHandler } = require('./routes/api');

// 创建Express应用
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// 配置中间件
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// 基本路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// API路由
app.use('/api', apiRouter);

// 初始化Socket处理器
const socketHandler = new SocketHandler(io);

// 设置API路由的SocketHandler引用
setSocketHandler(socketHandler);

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    socketHandler.destroy();
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`牛牛游戏服务器运行在端口 ${PORT}`);
    console.log(`游戏页面: http://localhost:${PORT}`);
    console.log(`管理页面: http://localhost:${PORT}/admin`);
});

module.exports = app;
