const Card = require('../src/models/Card');
const Deck = require('../src/models/Deck');
const NiuniuCalculator = require('../src/utils/NiuniuCalculator');

/**
 * 牌库系统测试
 * 测试Card类、Deck类和NiuniuCalculator类的功能
 */

console.log('🃏 开始测试牌库系统...\n');

// 测试Card类
function testCard() {
    console.log('📋 测试Card类:');
    
    // 创建测试牌
    const aceHearts = new Card('hearts', 1);
    const kingSpades = new Card('spades', 13);
    const tenClubs = new Card('clubs', 10);
    
    console.log(`  ✓ 红桃A: ${aceHearts.display} (值:${aceHearts.value}, 颜色:${aceHearts.color})`);
    console.log(`  ✓ 黑桃K: ${kingSpades.display} (值:${kingSpades.value}, 颜色:${kingSpades.color})`);
    console.log(`  ✓ 梅花10: ${tenClubs.display} (值:${tenClubs.value}, 颜色:${tenClubs.color})`);
    
    // 测试比较功能
    console.log(`  ✓ K > A: ${kingSpades.compareTo(aceHearts) > 0}`);
    console.log(`  ✓ A < K: ${aceHearts.compareTo(kingSpades) < 0}`);
    
    console.log('');
}

// 测试Deck类
function testDeck() {
    console.log('🎴 测试Deck类:');
    
    const deck = new Deck();
    console.log(`  ✓ 初始化牌库: ${deck.getRemainingCount()}张牌`);
    
    // 洗牌
    deck.shuffle();
    console.log(`  ✓ 洗牌完成`);
    
    // 发牌测试
    const hand = deck.dealCards(5);
    console.log(`  ✓ 发5张牌: ${hand.map(card => card.display).join(', ')}`);
    console.log(`  ✓ 剩余牌数: ${deck.getRemainingCount()}张`);
    
    // 多人发牌测试
    deck.reset();
    const playerHands = deck.dealToPlayers(4, 5);
    console.log(`  ✓ 为4个玩家各发5张牌:`);
    playerHands.forEach((hand, index) => {
        console.log(`    玩家${index + 1}: ${hand.map(card => card.display).join(', ')}`);
    });
    
    console.log('');
}

// 测试牛牛计算
function testNiuniuCalculator() {
    console.log('🐂 测试牛牛计算:');
    
    // 创建测试手牌
    const testHands = [
        // 牛牛
        [
            new Card('hearts', 10),
            new Card('spades', 13),
            new Card('clubs', 12),
            new Card('diamonds', 5),
            new Card('hearts', 5)
        ],
        // 牛8
        [
            new Card('hearts', 3),
            new Card('spades', 4),
            new Card('clubs', 3),
            new Card('diamonds', 6),
            new Card('hearts', 2)
        ],
        // 没牛
        [
            new Card('hearts', 1),
            new Card('spades', 3),
            new Card('clubs', 5),
            new Card('diamonds', 7),
            new Card('hearts', 9)
        ]
    ];
    
    testHands.forEach((hand, index) => {
        try {
            const result = NiuniuCalculator.calculateNiuniu(hand);
            console.log(`  ✓ 测试手牌${index + 1}:`);
            console.log(`    手牌: ${hand.map(card => card.display).join(', ')}`);
            console.log(`    结果: ${result.type} (${result.points}点, ${result.multiplier}倍)`);
            console.log(`    说明: ${result.description}`);
        } catch (error) {
            console.log(`  ✗ 测试手牌${index + 1}失败: ${error.message}`);
        }
        console.log('');
    });
}

// 运行所有测试
function runAllTests() {
    try {
        testCard();
        testDeck();
        testNiuniuCalculator();
        
        console.log('🎉 所有测试完成！牌库系统工作正常。');
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testCard,
    testDeck,
    testNiuniuCalculator,
    runAllTests
};
