const GameManager = require('../src/controllers/GameManager');

/**
 * 游戏管理器测试
 * 测试完整的游戏流程
 */

console.log('🎮 开始测试游戏管理器...\n');

function testGameManager() {
    console.log('📋 测试游戏管理器:');
    
    const gameManager = new GameManager();
    
    try {
        // 测试添加玩家
        console.log('  ✓ 添加玩家测试:');
        gameManager.addPlayer('player1', '小明');
        gameManager.addPlayer('player2', '小红');
        gameManager.addPlayer('player3', '小刚');
        
        const players = gameManager.getAllPlayers();
        console.log(`    - 当前玩家数: ${players.length}`);
        players.forEach(player => {
            console.log(`    - ${player.name} (ID: ${player.id})`);
        });
        
        // 测试游戏状态
        console.log('\n  ✓ 游戏状态测试:');
        const initialState = gameManager.getGameState();
        console.log(`    - 初始状态: ${initialState.state}`);
        console.log(`    - 可以开始游戏: ${gameManager.canStartGame()}`);
        
        // 测试开始游戏
        console.log('\n  ✓ 开始游戏测试:');
        const gameStart = gameManager.startNewGame();
        console.log(`    - 游戏状态: ${gameStart.gameState}`);
        console.log(`    - 当前轮次: ${gameStart.round}`);
        console.log(`    - 玩家数量: ${gameStart.playerCount}`);
        
        // 测试发牌
        console.log('\n  ✓ 发牌测试:');
        const hands = gameManager.dealCards();
        console.log('    - 所有玩家手牌:');
        Object.entries(hands).forEach(([playerId, playerData]) => {
            const cardDisplays = playerData.cards.map(card => card.display).join(', ');
            console.log(`      ${playerData.name}: ${cardDisplays}`);
        });
        
        // 测试计算结果
        console.log('\n  ✓ 计算结果测试:');
        const results = gameManager.calculateResults();
        console.log('    - 游戏结果 (按排名):');
        results.forEach((result, index) => {
            console.log(`      ${index + 1}. ${result.playerName}: ${result.type} (${result.points}点)`);
            const cardDisplays = result.cards.map(card => card.display).join(', ');
            console.log(`         手牌: ${cardDisplays}`);
        });
        
        // 测试玩家统计
        console.log('\n  ✓ 玩家统计测试:');
        players.forEach(player => {
            const info = gameManager.getPlayerInfo(player.id);
            console.log(`    - ${info.name}: 胜率 ${info.stats.winRate}% (${info.stats.totalWins}胜/${info.stats.totalGames}局)`);
        });
        
        // 测试游戏历史
        console.log('\n  ✓ 游戏历史测试:');
        const history = gameManager.getGameHistory();
        history.forEach(game => {
            console.log(`    - 第${game.round}轮: ${game.winner} 获胜 (${game.winnerType} - ${game.winnerPoints}点)`);
        });
        
        // 测试第二轮游戏
        console.log('\n  ✓ 第二轮游戏测试:');
        gameManager.startNewGame();
        gameManager.dealCards();
        const secondResults = gameManager.calculateResults();
        console.log(`    - 第二轮获胜者: ${secondResults[0].playerName} (${secondResults[0].type})`);
        
        // 最终统计
        console.log('\n  ✓ 最终统计:');
        const finalHistory = gameManager.getGameHistory();
        console.log(`    - 总游戏轮次: ${finalHistory.length}`);
        
        const finalPlayers = gameManager.getAllPlayers();
        finalPlayers.forEach(player => {
            const info = gameManager.getPlayerInfo(player.id);
            console.log(`    - ${info.name}: 胜率 ${info.stats.winRate}% (${info.stats.totalWins}胜/${info.stats.totalGames}局)`);
        });
        
        console.log('\n🎉 游戏管理器测试完成！所有功能正常。');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 测试错误处理
function testErrorHandling() {
    console.log('\n🚨 测试错误处理:');
    
    const gameManager = new GameManager();
    
    try {
        // 测试玩家不足时开始游戏
        console.log('  ✓ 测试玩家不足时开始游戏:');
        try {
            gameManager.startNewGame();
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        // 测试重复添加玩家
        console.log('  ✓ 测试重复添加玩家:');
        gameManager.addPlayer('test1', '测试1');
        try {
            gameManager.addPlayer('test1', '测试1重复');
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        console.log('\n🎉 错误处理测试完成！');
        
    } catch (error) {
        console.error('❌ 错误处理测试失败:', error.message);
    }
}

// 运行所有测试
function runAllTests() {
    testGameManager();
    testErrorHandling();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testGameManager,
    testErrorHandling,
    runAllTests
};
