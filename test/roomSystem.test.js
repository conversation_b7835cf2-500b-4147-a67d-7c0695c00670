const Room = require('../src/models/Room');
const RoomManager = require('../src/controllers/RoomManager');

/**
 * 房间管理系统测试
 * 测试Room类和RoomManager类的功能
 */

console.log('🏠 开始测试房间管理系统...\n');

// 测试Room类
function testRoom() {
    console.log('📋 测试Room类:');
    
    try {
        // 创建房间
        const roomCode = Room.generateRoomCode();
        const room = new Room(roomCode, 'admin1', '管理员');
        
        console.log(`  ✓ 房间创建成功: ${roomCode}`);
        console.log(`  ✓ 管理员: ${room.adminName}`);
        console.log(`  ✓ 初始状态: ${room.status}`);
        
        // 测试添加玩家
        console.log('\n  ✓ 测试添加玩家:');
        room.addPlayer('player1', '玩家1');
        room.addPlayer('player2', '玩家2');
        
        const players = room.getAllPlayers();
        console.log(`    - 当前玩家数: ${players.length}`);
        players.forEach(player => {
            console.log(`    - ${player.name} (管理员: ${player.isAdmin})`);
        });
        
        // 测试玩家准备状态
        console.log('\n  ✓ 测试玩家准备状态:');
        room.setPlayerReady('admin1', true);
        room.setPlayerReady('player1', true);
        room.setPlayerReady('player2', true);
        
        console.log(`    - 所有玩家准备好: ${room.areAllPlayersReady()}`);
        console.log(`    - 可以开始游戏: ${room.canStartGame()}`);
        
        // 测试开始游戏
        console.log('\n  ✓ 测试开始游戏:');
        const gameInfo = room.startGame();
        console.log(`    - 游戏状态: ${gameInfo.status}`);
        console.log(`    - 当前轮次: ${gameInfo.round}`);
        
        // 测试房间状态
        console.log('\n  ✓ 房间状态:');
        const status = room.getRoomStatus();
        console.log(`    - 房间码: ${status.roomCode}`);
        console.log(`    - 状态: ${status.status}`);
        console.log(`    - 玩家数: ${status.playerCount}/${status.maxPlayers}`);
        
        console.log('');
        
    } catch (error) {
        console.error('❌ Room测试失败:', error.message);
    }
}

// 测试RoomManager类
function testRoomManager() {
    console.log('🏢 测试RoomManager类:');
    
    try {
        const roomManager = new RoomManager();
        
        // 测试创建房间
        console.log('  ✓ 测试创建房间:');
        const room1 = roomManager.createRoom('admin1', '管理员1');
        const room2 = roomManager.createRoom('admin2', '管理员2');
        
        console.log(`    - 房间1: ${room1.roomCode} (${room1.room.adminName})`);
        console.log(`    - 房间2: ${room2.roomCode} (${room2.room.adminName})`);
        
        // 测试加入房间
        console.log('\n  ✓ 测试加入房间:');
        const joinResult1 = roomManager.joinRoom(room1.roomCode, 'player1', '玩家1');
        const joinResult2 = roomManager.joinRoom(room1.roomCode, 'player2', '玩家2');
        
        console.log(`    - 玩家1加入房间${room1.roomCode}: ${joinResult1.message}`);
        console.log(`    - 玩家2加入房间${room1.roomCode}: ${joinResult2.message}`);
        console.log(`    - 房间${room1.roomCode}玩家数: ${joinResult2.room.playerCount}`);
        
        // 测试房间查找
        console.log('\n  ✓ 测试房间查找:');
        const foundRoom = roomManager.getRoom(room1.roomCode);
        const playerRoom = roomManager.getRoomByPlayer('player1');
        
        console.log(`    - 通过房间码查找: ${foundRoom ? '成功' : '失败'}`);
        console.log(`    - 通过玩家ID查找: ${playerRoom ? '成功' : '失败'}`);
        console.log(`    - 玩家1所在房间: ${roomManager.getPlayerRoomCode('player1')}`);
        
        // 测试统计信息
        console.log('\n  ✓ 测试统计信息:');
        const stats = roomManager.getStats();
        console.log(`    - 总房间数: ${stats.totalRooms}`);
        console.log(`    - 活跃房间数: ${stats.activeRooms}`);
        console.log(`    - 总玩家数: ${stats.totalPlayers}`);
        console.log(`    - 平均每房间玩家数: ${stats.averagePlayersPerRoom}`);
        
        // 测试离开房间
        console.log('\n  ✓ 测试离开房间:');
        const leaveResult = roomManager.leaveRoom('player2');
        console.log(`    - 玩家2离开: ${leaveResult.message}`);
        
        const updatedStats = roomManager.getStats();
        console.log(`    - 更新后总玩家数: ${updatedStats.totalPlayers}`);
        
        // 清理
        roomManager.destroy();
        console.log('');
        
    } catch (error) {
        console.error('❌ RoomManager测试失败:', error.message);
        console.error(error.stack);
    }
}

// 测试错误处理
function testErrorHandling() {
    console.log('🚨 测试错误处理:');
    
    const roomManager = new RoomManager();
    
    try {
        // 测试加入不存在的房间
        console.log('  ✓ 测试加入不存在的房间:');
        try {
            roomManager.joinRoom('999999', 'player1', '玩家1');
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        // 测试无效房间码
        console.log('  ✓ 测试无效房间码:');
        try {
            roomManager.joinRoom('abc123', 'player1', '玩家1');
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        // 测试重复创建房间
        console.log('  ✓ 测试重复创建房间:');
        roomManager.createRoom('admin1', '管理员1');
        try {
            roomManager.createRoom('admin1', '管理员1重复');
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        // 测试房间满员
        console.log('  ✓ 测试房间满员:');
        const room = roomManager.createRoom('admin2', '管理员2');
        const testRoom = roomManager.getRoom(room.roomCode);
        testRoom.maxPlayers = 2; // 设置最大玩家数为2
        
        try {
            roomManager.joinRoom(room.roomCode, 'player1', '玩家1');
            roomManager.joinRoom(room.roomCode, 'player2', '玩家2'); // 这应该失败
            console.log('    ❌ 应该抛出错误');
        } catch (error) {
            console.log(`    ✓ 正确捕获错误: ${error.message}`);
        }
        
        roomManager.destroy();
        console.log('\n🎉 错误处理测试完成！');
        
    } catch (error) {
        console.error('❌ 错误处理测试失败:', error.message);
        roomManager.destroy();
    }
}

// 测试完整游戏流程
function testCompleteGameFlow() {
    console.log('🎮 测试完整游戏流程:');
    
    const roomManager = new RoomManager();
    
    try {
        // 创建房间并添加玩家
        const roomInfo = roomManager.createRoom('admin1', '管理员');
        const roomCode = roomInfo.roomCode;
        
        roomManager.joinRoom(roomCode, 'player1', '玩家1');
        roomManager.joinRoom(roomCode, 'player2', '玩家2');
        
        const room = roomManager.getRoom(roomCode);
        
        console.log(`  ✓ 房间创建并添加玩家完成: ${roomCode}`);
        console.log(`    - 玩家数: ${room.players.size}`);
        
        // 开始游戏
        const gameStart = room.startGame();
        console.log(`  ✓ 游戏开始: ${gameStart.status}`);
        
        // 发牌
        const dealResult = room.gameManager.dealCards();
        console.log(`  ✓ 发牌完成，玩家手牌:`);
        Object.entries(dealResult).forEach(([playerId, playerData]) => {
            console.log(`    - ${playerData.name}: ${playerData.cards.length}张牌`);
        });
        
        // 计算结果
        const results = room.gameManager.calculateResults();
        console.log(`  ✓ 游戏结果:`);
        results.forEach((result, index) => {
            console.log(`    ${index + 1}. ${result.playerName}: ${result.type} (${result.points}点)`);
        });
        
        // 结束游戏
        const gameEnd = room.endGame();
        console.log(`  ✓ 游戏结束: ${gameEnd.status}`);
        
        // 查看统计
        const players = room.getAllPlayers();
        console.log(`  ✓ 玩家统计:`);
        players.forEach(player => {
            console.log(`    - ${player.name}: 胜率 ${player.stats.winRate}%`);
        });
        
        roomManager.destroy();
        console.log('\n🎉 完整游戏流程测试完成！');
        
    } catch (error) {
        console.error('❌ 完整游戏流程测试失败:', error.message);
        console.error(error.stack);
        roomManager.destroy();
    }
}

// 运行所有测试
function runAllTests() {
    testRoom();
    testRoomManager();
    testErrorHandling();
    testCompleteGameFlow();
    
    console.log('🎉 所有房间管理系统测试完成！');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testRoom,
    testRoomManager,
    testErrorHandling,
    testCompleteGameFlow,
    runAllTests
};
