const io = require('socket.io-client');
const http = require('http');

// 简单的HTTP请求函数
function httpRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: options.headers || {}
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, json: () => Promise.resolve(jsonData) });
                } catch (e) {
                    resolve({ status: res.statusCode, text: () => Promise.resolve(data) });
                }
            });
        });

        req.on('error', reject);

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

/**
 * 端到端测试
 * 模拟完整的游戏流程，测试所有系统功能
 */

console.log('🎮 开始端到端测试...\n');

const SERVER_URL = 'http://localhost:3000';
const SOCKET_URL = 'http://localhost:3000';

// 模拟客户端类
class TestClient {
    constructor(name, isAdmin = false) {
        this.name = name;
        this.isAdmin = isAdmin;
        this.socket = null;
        this.roomCode = null;
        this.connected = false;
        this.events = [];
    }
    
    async connect() {
        return new Promise((resolve, reject) => {
            this.socket = io(SOCKET_URL);
            
            this.socket.on('connect', () => {
                this.connected = true;
                console.log(`  ✓ ${this.name} 连接成功`);
                resolve();
            });
            
            this.socket.on('disconnect', () => {
                this.connected = false;
                console.log(`  ✗ ${this.name} 断开连接`);
            });
            
            this.socket.on('connect_error', (error) => {
                reject(error);
            });
            
            // 监听所有事件
            const originalEmit = this.socket.emit;
            this.socket.emit = (...args) => {
                this.events.push({ type: 'emit', event: args[0], data: args[1] });
                return originalEmit.apply(this.socket, args);
            };
            
            const events = [
                'connected', 'roomCreated', 'joinSuccess', 'joinError', 'playersUpdate',
                'gameStart', 'cardsDealt', 'dealComplete', 'gameResults', 'gameEnd',
                'gameError', 'removedFromRoom', 'playerDisconnected'
            ];
            
            events.forEach(event => {
                this.socket.on(event, (data) => {
                    this.events.push({ type: 'receive', event, data });
                });
            });
        });
    }
    
    async createRoom() {
        return new Promise((resolve) => {
            this.socket.emit('createRoom', { adminName: this.name });
            
            this.socket.once('roomCreated', (data) => {
                this.roomCode = data.roomCode;
                console.log(`  ✓ ${this.name} 创建房间: ${this.roomCode}`);
                resolve(data);
            });
            
            this.socket.once('roomError', (error) => {
                console.log(`  ✗ ${this.name} 创建房间失败: ${error.message}`);
                resolve(null);
            });
        });
    }
    
    async joinRoom(roomCode) {
        return new Promise((resolve) => {
            this.socket.emit('joinGame', { playerName: this.name, roomCode });
            
            this.socket.once('joinSuccess', (data) => {
                this.roomCode = roomCode;
                console.log(`  ✓ ${this.name} 加入房间: ${roomCode}`);
                resolve(data);
            });
            
            this.socket.once('joinError', (error) => {
                console.log(`  ✗ ${this.name} 加入房间失败: ${error.message}`);
                resolve(null);
            });
        });
    }
    
    async startGame() {
        return new Promise((resolve) => {
            this.socket.emit('startGame');
            
            this.socket.once('gameStart', (data) => {
                console.log(`  ✓ ${this.name} 开始游戏`);
                resolve(data);
            });
            
            this.socket.once('gameError', (error) => {
                console.log(`  ✗ ${this.name} 开始游戏失败: ${error.message}`);
                resolve(null);
            });
        });
    }
    
    async dealCards() {
        return new Promise((resolve) => {
            this.socket.emit('dealCards');
            
            this.socket.once('dealComplete', (data) => {
                console.log(`  ✓ ${this.name} 发牌完成`);
                resolve(data);
            });
            
            this.socket.once('gameError', (error) => {
                console.log(`  ✗ ${this.name} 发牌失败: ${error.message}`);
                resolve(null);
            });
        });
    }
    
    async endGame() {
        return new Promise((resolve) => {
            this.socket.emit('endGame');
            
            this.socket.once('gameEnd', (data) => {
                console.log(`  ✓ ${this.name} 结束游戏`);
                resolve(data);
            });
            
            this.socket.once('gameError', (error) => {
                console.log(`  ✗ ${this.name} 结束游戏失败: ${error.message}`);
                resolve(null);
            });
        });
    }
    
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.connected = false;
        }
    }
    
    getLastEvent(eventName) {
        return this.events.filter(e => e.event === eventName).pop();
    }
    
    getEventCount(eventName) {
        return this.events.filter(e => e.event === eventName).length;
    }
}

// 测试API接口
async function testAPIEndpoints() {
    console.log('🌐 测试API接口:');
    
    try {
        // 测试健康检查
        const healthResponse = await httpRequest(`${SERVER_URL}/api/health`);
        const healthData = await healthResponse.json();
        console.log(`  ✓ 健康检查: ${healthData.status}`);

        // 测试服务器状态
        const statusResponse = await httpRequest(`${SERVER_URL}/api/status`);
        const statusData = await statusResponse.json();
        console.log(`  ✓ 服务器状态: ${statusData.data.status}`);
        console.log(`    - 运行时间: ${Math.floor(statusData.data.uptime)} 秒`);

        // 测试房间统计
        const statsResponse = await httpRequest(`${SERVER_URL}/api/rooms/stats`);
        const statsData = await statsResponse.json();
        console.log(`  ✓ 房间统计: ${statsData.data.totalRooms} 个房间, ${statsData.data.totalPlayers} 个玩家`);

        // 测试游戏规则
        const rulesResponse = await httpRequest(`${SERVER_URL}/api/game/rules`);
        const rulesData = await rulesResponse.json();
        console.log(`  ✓ 游戏规则: ${rulesData.data.title}`);
        
        console.log('');
        return true;
    } catch (error) {
        console.error('❌ API测试失败:', error.message);
        return false;
    }
}

// 测试完整游戏流程
async function testCompleteGameFlow() {
    console.log('🎮 测试完整游戏流程:');
    
    // 创建测试客户端
    const admin = new TestClient('管理员', true);
    const player1 = new TestClient('玩家1');
    const player2 = new TestClient('玩家2');
    const player3 = new TestClient('玩家3');
    
    try {
        // 1. 连接所有客户端
        console.log('  📡 连接客户端:');
        await admin.connect();
        await player1.connect();
        await player2.connect();
        await player3.connect();
        
        // 等待连接稳定
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 2. 管理员创建房间
        console.log('  🏠 创建房间:');
        const roomData = await admin.createRoom();
        if (!roomData) {
            throw new Error('创建房间失败');
        }
        
        const roomCode = roomData.roomCode;
        
        // 3. 玩家加入房间
        console.log('  👥 玩家加入房间:');
        await player1.joinRoom(roomCode);
        await player2.joinRoom(roomCode);
        await player3.joinRoom(roomCode);
        
        // 等待玩家列表更新
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 4. 验证房间状态
        console.log('  🔍 验证房间状态:');
        const validateResponse = await httpRequest(`${SERVER_URL}/api/rooms/validate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ roomCode })
        });
        const validateData = await validateResponse.json();
        console.log(`    - 房间存在: ${validateData.data.exists}`);
        console.log(`    - 可以加入: ${validateData.data.canJoin}`);
        
        // 5. 开始游戏
        console.log('  🚀 开始游戏:');
        const gameStartData = await admin.startGame();
        if (!gameStartData) {
            throw new Error('开始游戏失败');
        }
        
        // 等待游戏开始事件传播
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 6. 发牌
        console.log('  🃏 发牌:');
        const dealData = await admin.dealCards();
        if (!dealData) {
            throw new Error('发牌失败');
        }
        
        // 等待发牌完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 7. 检查玩家是否收到手牌
        console.log('  📋 检查手牌:');
        const players = [admin, player1, player2, player3];
        players.forEach(player => {
            const cardsEvent = player.getLastEvent('cardsDealt');
            if (cardsEvent) {
                console.log(`    - ${player.name}: 收到 ${cardsEvent.data.cards.length} 张牌, 结果: ${cardsEvent.data.result.type}`);
            } else {
                console.log(`    - ${player.name}: 未收到手牌`);
            }
        });
        
        // 8. 结束游戏
        console.log('  🏁 结束游戏:');
        const endData = await admin.endGame();
        if (!endData) {
            throw new Error('结束游戏失败');
        }
        
        // 等待游戏结束事件传播
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 9. 检查游戏结果
        console.log('  🏆 检查游戏结果:');
        players.forEach(player => {
            const resultsEvent = player.getLastEvent('gameResults');
            if (resultsEvent && resultsEvent.data && resultsEvent.data.length > 0) {
                console.log(`    - 获胜者: ${resultsEvent.data[0].playerName} (${resultsEvent.data[0].type})`);
                console.log(`    - 参与人数: ${resultsEvent.data.length}`);
            } else {
                console.log(`    - ${player.name}: 未收到游戏结果`);
            }
        });
        
        // 10. 获取最终房间状态
        console.log('  📊 最终房间状态:');
        const finalStatsResponse = await httpRequest(`${SERVER_URL}/api/rooms/stats`);
        const finalStatsData = await finalStatsResponse.json();
        console.log(`    - 总房间数: ${finalStatsData.data.totalRooms}`);
        console.log(`    - 活跃房间数: ${finalStatsData.data.activeRooms}`);
        console.log(`    - 总玩家数: ${finalStatsData.data.totalPlayers}`);
        
        // 11. 断开连接
        console.log('  🔌 断开连接:');
        admin.disconnect();
        player1.disconnect();
        player2.disconnect();
        player3.disconnect();
        
        console.log('');
        return true;
        
    } catch (error) {
        console.error('❌ 游戏流程测试失败:', error.message);
        
        // 清理连接
        admin.disconnect();
        player1.disconnect();
        player2.disconnect();
        player3.disconnect();
        
        return false;
    }
}

// 测试错误场景
async function testErrorScenarios() {
    console.log('🚨 测试错误场景:');
    
    const client = new TestClient('测试客户端');
    
    try {
        await client.connect();
        
        // 测试加入不存在的房间
        console.log('  ✓ 测试加入不存在的房间:');
        const joinResult = await client.joinRoom('999999');
        if (!joinResult) {
            console.log('    - 正确拒绝了无效房间');
        }
        
        // 测试非管理员开始游戏
        console.log('  ✓ 测试非管理员开始游戏:');
        const startResult = await client.startGame();
        if (!startResult) {
            console.log('    - 正确拒绝了非管理员操作');
        }
        
        client.disconnect();
        console.log('');
        return true;
        
    } catch (error) {
        console.error('❌ 错误场景测试失败:', error.message);
        client.disconnect();
        return false;
    }
}

// 运行所有端到端测试
async function runE2ETests() {
    console.log('🧪 开始完整端到端测试\n');
    
    const results = [];
    
    // 测试API接口
    results.push(await testAPIEndpoints());
    
    // 测试完整游戏流程
    results.push(await testCompleteGameFlow());
    
    // 测试错误场景
    results.push(await testErrorScenarios());
    
    // 总结结果
    const passedTests = results.filter(r => r).length;
    const totalTests = results.length;
    
    console.log('📊 测试结果总结:');
    console.log(`  ✅ 通过: ${passedTests}/${totalTests}`);
    console.log(`  📈 成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有端到端测试通过！系统功能完整！');
    } else {
        console.log('\n⚠️  部分测试失败，请检查系统状态');
    }
    
    return passedTests === totalTests;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runE2ETests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    testAPIEndpoints,
    testCompleteGameFlow,
    testErrorScenarios,
    runE2ETests
};
