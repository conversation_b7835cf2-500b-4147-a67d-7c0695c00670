const NiuniuCalculator = require('../src/utils/NiuniuCalculator');
const Card = require('../src/models/Card');

// 测试用户报告的案例
console.log('测试牛牛计算逻辑');

// 测试案例1: K 8 A A 10 应该是牛牛(10点)
console.log('\n测试案例1: K 8 A A 10');
const cards1 = [
    new Card('spades', 13), // K
    new Card('hearts', 8),  // 8
    new Card('diamonds', 1), // A
    new Card('clubs', 1),   // A
    new Card('hearts', 10)  // 10
];

console.log('手牌:');
cards1.forEach((card, index) => {
    console.log(`${index + 1}. ${card.display} (值: ${card.value})`);
});

const result1 = NiuniuCalculator.calculateNiuniu(cards1);
console.log(`计算结果: 牌型: ${result1.type} | 点数: ${result1.points}`);

// 测试案例2: 8 6 8 Q 2
console.log('\n测试案例2: 8 6 8 Q 2');
const cards2 = [
    new Card('spades', 8),
    new Card('hearts', 6),
    new Card('diamonds', 8),
    new Card('clubs', 12), // Q
    new Card('hearts', 2)
];

console.log('手牌:');
cards2.forEach((card, index) => {
    console.log(`${index + 1}. ${card.display} (值: ${card.value})`);
});

const result2 = NiuniuCalculator.calculateNiuniu(cards2);
console.log(`计算结果: 牌型: ${result2.type} | 点数: ${result2.points}`);

// 测试案例3: K 5 2 2 8
console.log('\n测试案例3: K 5 2 2 8');
const cards3 = [
    new Card('spades', 13), // K
    new Card('hearts', 5),
    new Card('diamonds', 2),
    new Card('clubs', 2),
    new Card('hearts', 8)
];

console.log('手牌:');
cards3.forEach((card, index) => {
    console.log(`${index + 1}. ${card.display} (值: ${card.value})`);
});

const result3 = NiuniuCalculator.calculateNiuniu(cards3);
console.log(`计算结果: 牌型: ${result3.type} | 点数: ${result3.points}`);