const SocketHandler = require('../src/controllers/SocketHandler');
const { Server } = require('socket.io');
const { createServer } = require('http');

/**
 * 房间管理系统集成测试
 * 模拟Socket.io连接和消息传递
 */

console.log('🔗 开始集成测试...\n');

// 模拟Socket对象
class MockSocket {
    constructor(id) {
        this.id = id;
        this.rooms = new Set();
        this.events = new Map();
        this.emittedEvents = [];
    }
    
    on(event, handler) {
        this.events.set(event, handler);
    }
    
    emit(event, data) {
        this.emittedEvents.push({ event, data });
        console.log(`    Socket ${this.id} emit: ${event}`, data?.message || data?.success || '');
    }
    
    join(room) {
        this.rooms.add(room);
        console.log(`    Socket ${this.id} joined room: ${room}`);
    }
    
    leave(room) {
        this.rooms.delete(room);
        console.log(`    Socket ${this.id} left room: ${room}`);
    }
    
    trigger(event, data) {
        const handler = this.events.get(event);
        if (handler) {
            handler(data);
        }
    }
    
    getLastEmit(event) {
        return this.emittedEvents.filter(e => e.event === event).pop();
    }
}

// 模拟IO对象
class MockIO {
    constructor() {
        this.sockets = new Map();
        this.rooms = new Map();
        this.events = new Map();
    }
    
    on(event, handler) {
        this.events.set(event, handler);
    }
    
    to(room) {
        return {
            emit: (event, data) => {
                console.log(`    Broadcast to room ${room}: ${event}`, data?.message || '');
                // 模拟广播到房间内的所有socket
                for (const socket of this.sockets.values()) {
                    if (socket.rooms.has(room)) {
                        socket.emit(event, data);
                    }
                }
            }
        };
    }
    
    addSocket(socket) {
        this.sockets.set(socket.id, socket);
        socket.sockets = { sockets: this.sockets };
        
        // 触发连接事件
        const connectionHandler = this.events.get('connection');
        if (connectionHandler) {
            connectionHandler(socket);
        }
    }
    
    removeSocket(socketId) {
        this.sockets.delete(socketId);
    }
}

// 测试房间创建和加入
function testRoomCreationAndJoin() {
    console.log('🏠 测试房间创建和加入:');
    
    const mockIO = new MockIO();
    const socketHandler = new SocketHandler(mockIO);
    
    // 创建模拟socket
    const adminSocket = new MockSocket('admin1');
    const playerSocket1 = new MockSocket('player1');
    const playerSocket2 = new MockSocket('player2');
    
    // 添加到IO
    mockIO.addSocket(adminSocket);
    mockIO.addSocket(playerSocket1);
    mockIO.addSocket(playerSocket2);
    
    try {
        // 管理员创建房间
        console.log('  ✓ 管理员创建房间:');
        adminSocket.trigger('createRoom', { adminName: '管理员' });
        
        const roomCreated = adminSocket.getLastEmit('roomCreated');
        if (roomCreated && roomCreated.data.success) {
            const roomCode = roomCreated.data.roomCode;
            console.log(`    - 房间创建成功: ${roomCode}`);
            
            // 玩家1加入房间
            console.log('  ✓ 玩家1加入房间:');
            playerSocket1.trigger('joinGame', {
                playerName: '玩家1',
                roomCode: roomCode
            });
            
            const joinSuccess1 = playerSocket1.getLastEmit('joinSuccess');
            if (joinSuccess1 && joinSuccess1.data.success) {
                console.log(`    - 玩家1加入成功`);
                
                // 玩家2加入房间
                console.log('  ✓ 玩家2加入房间:');
                playerSocket2.trigger('joinGame', {
                    playerName: '玩家2',
                    roomCode: roomCode
                });
                
                const joinSuccess2 = playerSocket2.getLastEmit('joinSuccess');
                if (joinSuccess2 && joinSuccess2.data.success) {
                    console.log(`    - 玩家2加入成功`);
                    console.log(`    - 房间玩家数: ${joinSuccess2.data.room.playerCount}`);
                } else {
                    console.log('    ❌ 玩家2加入失败');
                }
            } else {
                console.log('    ❌ 玩家1加入失败');
            }
        } else {
            console.log('    ❌ 房间创建失败');
        }
        
        socketHandler.destroy();
        console.log('');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        socketHandler.destroy();
    }
}

// 测试游戏流程
function testGameFlow() {
    console.log('🎮 测试游戏流程:');
    
    const mockIO = new MockIO();
    const socketHandler = new SocketHandler(mockIO);
    
    const adminSocket = new MockSocket('admin1');
    const playerSocket1 = new MockSocket('player1');
    const playerSocket2 = new MockSocket('player2');
    
    mockIO.addSocket(adminSocket);
    mockIO.addSocket(playerSocket1);
    mockIO.addSocket(playerSocket2);
    
    try {
        // 创建房间并添加玩家
        adminSocket.trigger('createRoom', { adminName: '管理员' });
        const roomCreated = adminSocket.getLastEmit('roomCreated');
        const roomCode = roomCreated.data.roomCode;
        
        playerSocket1.trigger('joinGame', {
            playerName: '玩家1',
            roomCode: roomCode
        });
        
        playerSocket2.trigger('joinGame', {
            playerName: '玩家2',
            roomCode: roomCode
        });
        
        console.log(`  ✓ 房间设置完成: ${roomCode}`);
        
        // 开始游戏
        console.log('  ✓ 开始游戏:');
        adminSocket.trigger('startGame');
        
        const gameStart = adminSocket.getLastEmit('gameStart');
        if (gameStart) {
            console.log(`    - 游戏状态: ${gameStart.data.status}`);
        }
        
        // 发牌
        console.log('  ✓ 发牌:');
        adminSocket.trigger('dealCards');
        
        // 检查玩家是否收到手牌
        const player1Cards = playerSocket1.getLastEmit('cardsDealt');
        const player2Cards = playerSocket2.getLastEmit('cardsDealt');
        
        if (player1Cards && player2Cards) {
            console.log(`    - 玩家1收到 ${player1Cards.data.cards.length} 张牌`);
            console.log(`    - 玩家2收到 ${player2Cards.data.cards.length} 张牌`);
            console.log(`    - 玩家1结果: ${player1Cards.data.result.type}`);
            console.log(`    - 玩家2结果: ${player2Cards.data.result.type}`);
        }
        
        // 结束游戏
        console.log('  ✓ 结束游戏:');
        adminSocket.trigger('endGame');
        
        const gameResults = adminSocket.getLastEmit('gameResults');
        if (gameResults) {
            console.log(`    - 获胜者: ${gameResults.data[0].playerName}`);
            console.log(`    - 获胜牌型: ${gameResults.data[0].type}`);
        }
        
        socketHandler.destroy();
        console.log('');
        
    } catch (error) {
        console.error('❌ 游戏流程测试失败:', error.message);
        socketHandler.destroy();
    }
}

// 测试错误处理
function testErrorHandling() {
    console.log('🚨 测试错误处理:');
    
    const mockIO = new MockIO();
    const socketHandler = new SocketHandler(mockIO);
    
    const socket = new MockSocket('test1');
    mockIO.addSocket(socket);
    
    try {
        // 测试加入不存在的房间
        console.log('  ✓ 测试加入不存在的房间:');
        socket.trigger('joinGame', {
            playerName: '测试玩家',
            roomCode: '999999'
        });
        
        const joinError = socket.getLastEmit('joinError');
        if (joinError && !joinError.data.success) {
            console.log(`    - 正确捕获错误: ${joinError.data.message}`);
        }
        
        // 测试非管理员开始游戏
        console.log('  ✓ 测试非管理员开始游戏:');
        socket.trigger('startGame');
        
        const gameError = socket.getLastEmit('gameError');
        if (gameError && !gameError.data.success) {
            console.log(`    - 正确捕获错误: ${gameError.data.message}`);
        }
        
        socketHandler.destroy();
        console.log('');
        
    } catch (error) {
        console.error('❌ 错误处理测试失败:', error.message);
        socketHandler.destroy();
    }
}

// 测试断线处理
function testDisconnectHandling() {
    console.log('🔌 测试断线处理:');
    
    const mockIO = new MockIO();
    const socketHandler = new SocketHandler(mockIO);
    
    const adminSocket = new MockSocket('admin1');
    const playerSocket = new MockSocket('player1');
    
    mockIO.addSocket(adminSocket);
    mockIO.addSocket(playerSocket);
    
    try {
        // 创建房间并添加玩家
        adminSocket.trigger('createRoom', { adminName: '管理员' });
        const roomCreated = adminSocket.getLastEmit('roomCreated');
        const roomCode = roomCreated.data.roomCode;
        
        playerSocket.trigger('joinGame', {
            playerName: '玩家1',
            roomCode: roomCode
        });
        
        console.log(`  ✓ 房间设置完成: ${roomCode}`);
        
        // 模拟玩家断线
        console.log('  ✓ 模拟玩家断线:');
        playerSocket.trigger('disconnect');
        
        // 检查房间状态
        adminSocket.trigger('getGameState');
        const gameState = adminSocket.getLastEmit('gameState');
        if (gameState) {
            console.log(`    - 断线后房间玩家数: ${gameState.data.players.length}`);
        }
        
        socketHandler.destroy();
        console.log('');
        
    } catch (error) {
        console.error('❌ 断线处理测试失败:', error.message);
        socketHandler.destroy();
    }
}

// 运行所有集成测试
function runAllIntegrationTests() {
    testRoomCreationAndJoin();
    testGameFlow();
    testErrorHandling();
    testDisconnectHandling();
    
    console.log('🎉 所有集成测试完成！');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllIntegrationTests();
}

module.exports = {
    testRoomCreationAndJoin,
    testGameFlow,
    testErrorHandling,
    testDisconnectHandling,
    runAllIntegrationTests
};
