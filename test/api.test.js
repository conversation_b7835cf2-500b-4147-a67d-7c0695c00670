const http = require('http');
const express = require('express');
const socketIo = require('socket.io');
const SocketHandler = require('../src/controllers/SocketHandler');
const { router: apiRouter, setSocketHandler } = require('../routes/api');

/**
 * API和WebSocket集成测试
 * 测试Express API路由和Socket.io的集成
 */

console.log('🔗 开始API和WebSocket集成测试...\n');

// 创建测试服务器
function createTestServer() {
    const app = express();
    const server = http.createServer(app);
    const io = socketIo(server);
    
    // 配置中间件
    app.use(express.json());
    app.use('/api', apiRouter);
    
    // 初始化Socket处理器
    const socketHandler = new SocketHandler(io);
    setSocketHandler(socketHandler);
    
    return { app, server, io, socketHandler };
}

// 测试API路由
async function testAPIRoutes() {
    console.log('🌐 测试API路由:');
    
    const { server, socketHandler } = createTestServer();
    
    try {
        // 启动服务器
        await new Promise((resolve) => {
            server.listen(0, () => {
                console.log(`  ✓ 测试服务器启动在端口 ${server.address().port}`);
                resolve();
            });
        });
        
        const port = server.address().port;
        const baseURL = `http://localhost:${port}`;
        
        // 测试健康检查
        console.log('  ✓ 测试健康检查:');
        const healthResponse = await fetch(`${baseURL}/api/health`);
        const healthData = await healthResponse.json();
        console.log(`    - 状态: ${healthData.status}`);
        console.log(`    - 响应: ${healthData.success ? '成功' : '失败'}`);
        
        // 测试服务器状态
        console.log('  ✓ 测试服务器状态:');
        const statusResponse = await fetch(`${baseURL}/api/status`);
        const statusData = await statusResponse.json();
        console.log(`    - 运行状态: ${statusData.data.status}`);
        console.log(`    - 运行时间: ${Math.floor(statusData.data.uptime)} 秒`);
        
        // 测试房间统计
        console.log('  ✓ 测试房间统计:');
        const statsResponse = await fetch(`${baseURL}/api/rooms/stats`);
        const statsData = await statsResponse.json();
        console.log(`    - 总房间数: ${statsData.data.totalRooms}`);
        console.log(`    - 总玩家数: ${statsData.data.totalPlayers}`);
        
        // 测试房间验证
        console.log('  ✓ 测试房间验证:');
        const validateResponse = await fetch(`${baseURL}/api/rooms/validate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ roomCode: '123456' })
        });
        const validateData = await validateResponse.json();
        console.log(`    - 房间存在: ${validateData.data.exists}`);
        console.log(`    - 可以加入: ${validateData.data.canJoin}`);
        
        // 测试游戏规则
        console.log('  ✓ 测试游戏规则:');
        const rulesResponse = await fetch(`${baseURL}/api/game/rules`);
        const rulesData = await rulesResponse.json();
        console.log(`    - 规则标题: ${rulesData.data.title}`);
        console.log(`    - 规则数量: ${rulesData.data.rules.length}`);
        console.log(`    - 牌型数量: ${rulesData.data.cardTypes.length}`);
        
        // 关闭服务器
        server.close();
        socketHandler.destroy();
        
        console.log('');
        
    } catch (error) {
        console.error('❌ API测试失败:', error.message);
        server.close();
        socketHandler.destroy();
    }
}

// 测试WebSocket和API的集成
async function testWebSocketAPIIntegration() {
    console.log('🔄 测试WebSocket和API集成:');
    
    const { server, socketHandler } = createTestServer();
    
    try {
        // 启动服务器
        await new Promise((resolve) => {
            server.listen(0, () => {
                resolve();
            });
        });
        
        const port = server.address().port;
        const baseURL = `http://localhost:${port}`;
        
        // 模拟创建房间
        console.log('  ✓ 模拟创建房间:');
        const roomCode = socketHandler.roomManager.createRoom('admin1', '测试管理员').roomCode;
        console.log(`    - 房间创建: ${roomCode}`);
        
        // 通过API验证房间
        const validateResponse = await fetch(`${baseURL}/api/rooms/validate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ roomCode })
        });
        const validateData = await validateResponse.json();
        console.log(`    - API验证房间存在: ${validateData.data.exists}`);
        
        // 通过API获取房间信息
        const roomResponse = await fetch(`${baseURL}/api/rooms/${roomCode}`);
        const roomData = await roomResponse.json();
        console.log(`    - 房间玩家数: ${roomData.data.playerCount}`);
        console.log(`    - 房间状态: ${roomData.data.status}`);
        
        // 添加玩家
        socketHandler.roomManager.joinRoom(roomCode, 'player1', '测试玩家1');
        socketHandler.roomManager.joinRoom(roomCode, 'player2', '测试玩家2');
        
        // 再次获取房间统计
        const updatedStatsResponse = await fetch(`${baseURL}/api/rooms/stats`);
        const updatedStatsData = await updatedStatsResponse.json();
        console.log(`    - 更新后总玩家数: ${updatedStatsData.data.totalPlayers}`);
        
        // 通过API清理房间
        const cleanupResponse = await fetch(`${baseURL}/api/rooms/cleanup`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ timeoutMinutes: 0 }) // 立即清理
        });
        const cleanupData = await cleanupResponse.json();
        console.log(`    - 清理房间数: ${cleanupData.data.cleanedCount}`);
        
        // 关闭服务器
        server.close();
        socketHandler.destroy();
        
        console.log('');
        
    } catch (error) {
        console.error('❌ WebSocket API集成测试失败:', error.message);
        server.close();
        socketHandler.destroy();
    }
}

// 测试错误处理
async function testErrorHandling() {
    console.log('🚨 测试API错误处理:');
    
    const { server, socketHandler } = createTestServer();
    
    try {
        // 启动服务器
        await new Promise((resolve) => {
            server.listen(0, () => {
                resolve();
            });
        });
        
        const port = server.address().port;
        const baseURL = `http://localhost:${port}`;
        
        // 测试获取不存在的房间
        console.log('  ✓ 测试获取不存在的房间:');
        const notFoundResponse = await fetch(`${baseURL}/api/rooms/999999`);
        console.log(`    - 状态码: ${notFoundResponse.status}`);
        const notFoundData = await notFoundResponse.json();
        console.log(`    - 错误信息: ${notFoundData.message}`);
        
        // 测试无效的房间验证请求
        console.log('  ✓ 测试无效的房间验证请求:');
        const invalidResponse = await fetch(`${baseURL}/api/rooms/validate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({}) // 缺少roomCode
        });
        console.log(`    - 状态码: ${invalidResponse.status}`);
        const invalidData = await invalidResponse.json();
        console.log(`    - 错误信息: ${invalidData.message}`);
        
        // 关闭服务器
        server.close();
        socketHandler.destroy();
        
        console.log('');
        
    } catch (error) {
        console.error('❌ 错误处理测试失败:', error.message);
        server.close();
        socketHandler.destroy();
    }
}

// 运行所有测试
async function runAllTests() {
    await testAPIRoutes();
    await testWebSocketAPIIntegration();
    await testErrorHandling();
    
    console.log('🎉 所有API和WebSocket集成测试完成！');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testAPIRoutes,
    testWebSocketAPIIntegration,
    testErrorHandling,
    runAllTests
};
