<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg8"
   version="1.1"
   viewBox="0 0 261.11999 355.20002"
   height="93.980003mm"
   width="69.087997mm"
   sodipodi:docname="HEART-10.svg"
   inkscape:version="1.1-dev (60faa23, 2020-06-06)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="774"
     inkscape:window-height="485"
     id="namedview33"
     showgrid="false"
     inkscape:zoom="0.28299806"
     inkscape:cx="209.91988"
     inkscape:cy="-18.472449"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="0"
     inkscape:current-layer="svg8" />
  <title
     id="title8745">Vector Playing Cards</title>
  <defs
     id="defs2" />
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Vector Playing Cards</dc:title>
        <dc:creator>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:rights>
          <cc:Agent>
            <dc:title>Copyright 2011, 017</dc:title>
          </cc:Agent>
        </dc:rights>
        <dc:publisher>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:description>Vector Playing Cards</dc:description>
        <dc:contributor>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:contributor>
        <cc:license
           rdf:resource="https://opensource.org/licenses/lgpl-3.0.html" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     transform="matrix(1.0966315,0,0,1.0679512,-1290.0699,-384.91994)"
     id="layer1">
    <g
       style="display:inline"
       id="HEART-10"
       transform="matrix(1.06667,0,0,1.06667,-2036.42,1495.37)">
      <g
         transform="translate(2751.57,-1434.28)"
         id="g10276-9-9"
         style="display:inline;opacity:1">
        <rect
           y="370.27557"
           x="260.43304"
           height="311.81104"
           width="223.22835"
           id="rect10278-0-6"
           style="display:inline;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.00057;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
           rx="10.677567"
           ry="10.664032" />
      </g>
      <g
         id="g10296-8-7"
         style="display:inline;opacity:1"
         transform="translate(3012,-1064)" />
      <g
         transform="translate(3038.59,-1083.91)"
         id="g10302-2-0"
         style="display:inline;opacity:1" />
      <path
         id="path10308-5-1"
         d="m -52.766444,-63.259199 c 0,0 -4.6663,-7.65547 -10.9609,-16.65626 -4.211,-6.02326 -7.5776,-12.54533 -7.8906,-18.16309 -0.359,-6.417311 3.0392,-12.855381 9.1914,-13.167411 6.1485,-0.31185 8.6442,4.94803 9.6601,9.28461 1.016,-4.33658 3.5117,-9.59646 9.6602,-9.28461 6.1522,0.31203 9.5504,6.7501 9.1914,13.167411 -0.313,5.61776 -3.6796,12.13983 -7.8906,18.16309 -6.2946,9.00079 -10.961,16.65626 -10.961,16.65626 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         id="path10310-7-8"
         d="m -95.396892,-140.12123 c 0,0 -2.2247,-3.18978 -5.225698,-6.9401 -2.0077,-2.50969 -3.6127,-5.22722 -3.762,-7.56795 -0.1711,-2.67388 1.449,-5.35641 4.3821,-5.48643 2.931398,-0.12993 4.121198,2.06168 4.605598,3.86859 0.4844,-1.80691 1.6743,-3.99852 4.6056,-3.86859 2.9331,0.13002 4.5533,2.81255 4.3821,5.48643 -0.1492,2.34073 -1.7543,5.05826 -3.7619,7.56795 -3.0011,3.75032 -5.2258,6.9401 -5.2258,6.9401 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         id="path10312-3-5"
         d="m 93.397604,31.743411 c 0,0 -2.2247,3.18978 -5.2257,6.9401 -2.0077,2.50969 -3.6127,5.22722 -3.762,7.56795 -0.1711,2.67388 1.449,5.35641 4.3821,5.48643 2.9314,0.12993 4.1212,-2.06168 4.6056,-3.86859 0.4844,1.80691 1.6743,3.99852 4.6056,3.86859 2.933096,-0.13002 4.553296,-2.81255 4.382096,-5.48643 -0.1492,-2.34073 -1.7543,-5.05826 -3.761896,-7.56795 -3.0011,-3.75032 -5.2258,-6.9401 -5.2258,-6.9401 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1;stroke:none;stroke-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m -1.000051,-86.7592 c 0,0 -4.6663,-7.655471 -10.9609,-16.65626 -4.211,-6.02326 -7.5776,-12.54533 -7.8906,-18.16309 -0.359,-6.41731 3.0392,-12.85538 9.1914,-13.16741 6.1485,-0.31185 8.6442,4.94803 9.6601,9.28461 1.01600005,-4.33658 3.5117,-9.59646 9.6602,-9.28461 6.1522,0.31203 9.5504,6.7501 9.1914,13.16741 -0.313,5.61776 -3.6796,12.13983 -7.8906,18.16309 -6.2946,9.000789 -10.961,16.65626 -10.961,16.65626 z"
         id="path10314-8-8"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m -52.766444,-129.39963 c 0,0 -4.6663,-7.65547 -10.9609,-16.65626 -4.211,-6.02326 -7.5776,-12.54533 -7.8906,-18.16309 -0.359,-6.41731 3.0392,-12.85538 9.1914,-13.16741 6.1485,-0.31185 8.6442,4.94803 9.6601,9.28461 1.016,-4.33658 3.5117,-9.59646 9.6602,-9.28461 6.1522,0.31203 9.5504,6.7501 9.1914,13.16741 -0.313,5.61776 -3.6796,12.13983 -7.8906,18.16309 -6.2946,9.00079 -10.961,16.65626 -10.961,16.65626 z"
         id="path10318-3-7"
         transform="translate(3124.62,-853.906)" />
      <path
         id="path10322-4-6"
         d="m 50.766354,-63.259199 c 0,0 -4.6663,-7.65547 -10.9609,-16.65626 -4.211,-6.02326 -7.5776,-12.54533 -7.8906,-18.16309 -0.359,-6.417311 3.0392,-12.855381 9.1914,-13.167411 6.1485,-0.31185 8.6442,4.94803 9.6601,9.28461 1.016,-4.33658 3.5117,-9.59646 9.6602,-9.28461 6.1522,0.31203 9.5504,6.7501 9.1914,13.167411 -0.313,5.61776 -3.6796,12.13983 -7.8906,18.16309 -6.2946,9.00079 -10.961,16.65626 -10.961,16.65626 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 50.766354,-129.39963 c 0,0 -4.6663,-7.65547 -10.9609,-16.65626 -4.211,-6.02326 -7.5776,-12.54533 -7.8906,-18.16309 -0.359,-6.41731 3.0392,-12.85538 9.1914,-13.16741 6.1485,-0.31185 8.6442,4.94803 9.6601,9.28461 1.016,-4.33658 3.5117,-9.59646 9.6602,-9.28461 6.1522,0.31203 9.5504,6.7501 9.1914,13.16741 -0.313,5.61776 -3.6796,12.13983 -7.8906,18.16309 -6.2946,9.00079 -10.961,16.65626 -10.961,16.65626 z"
         id="path10324-9-8"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m -1.00005,-21.61876 c 0,0 -4.6663,7.65547 -10.9609,16.6562704 -4.211,6.0232596 -7.5776,12.5453296 -7.8906,18.1630796 -0.359,6.41731 3.0392,12.85538 9.1914,13.16741 6.1485,0.31185 8.6442,-4.94803 9.6601,-9.28461 1.01600005,4.33658 3.5117,9.59646 9.6602,9.28461 6.1522,-0.31203 9.5504,-6.7501 9.1914,-13.16741 C 17.53855,7.58284 14.17195,1.06077 9.96095,-4.9624896 3.66635,-13.96329 -1.00005,-21.61876 -1.00005,-21.61876 Z"
         id="path10332-8-6"
         transform="translate(3124.62,-853.906)" />
      <path
         id="path10334-5-0"
         d="m 50.766354,-45.118759 c 0,0 -4.6663,7.65547 -10.9609,16.65626 -4.211,6.02326 -7.5776,12.54533 -7.8906,18.16309 -0.359,6.4173104 3.0392,12.8553804 9.1914,13.1674104 6.1485,0.31185 8.6442,-4.94803 9.6601,-9.28461 1.016,4.33658 3.5117,9.59646 9.6602,9.28461 6.1522,-0.31203 9.5504,-6.7501 9.1914,-13.1674104 -0.313,-5.61776 -3.6796,-12.13983 -7.8906,-18.16309 -6.2946,-9.00079 -10.961,-16.65626 -10.961,-16.65626 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m -52.766444,-45.118759 c 0,0 -4.6663,7.65547 -10.9609,16.65626 -4.211,6.02326 -7.5776,12.54533 -7.8906,18.16309 -0.359,6.4173104 3.0392,12.8553804 9.1914,13.1674104 6.1485,0.31185 8.6442,-4.94803 9.6601,-9.28461 1.016,4.33658 3.5117,9.59646 9.6602,9.28461 6.1522,-0.31203 9.5504,-6.7501 9.1914,-13.1674104 -0.313,-5.61776 -3.6796,-12.13983 -7.8906,-18.16309 -6.2946,-9.00079 -10.961,-16.65626 -10.961,-16.65626 z"
         id="path10336-3-5"
         transform="translate(3124.62,-853.906)" />
      <path
         id="path10338-2-9"
         d="m 50.766354,21.021681 c 0,0 -4.6663,7.65547 -10.9609,16.65626 -4.211,6.02326 -7.5776,12.54533 -7.8906,18.16309 -0.359,6.41731 3.0392,12.85538 9.1914,13.16741 6.1485,0.31185 8.6442,-4.94803 9.6601,-9.28461 1.016,4.33658 3.5117,9.59646 9.6602,9.28461 6.1522,-0.31203 9.5504,-6.7501 9.1914,-13.16741 -0.313,-5.61776 -3.6796,-12.13983 -7.8906,-18.16309 -6.2946,-9.00079 -10.961,-16.65626 -10.961,-16.65626 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1;stroke:none;stroke-opacity:1"
         transform="translate(3124.62,-853.906)" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m -52.766444,21.021681 c 0,0 -4.6663,7.65547 -10.9609,16.65626 -4.211,6.02326 -7.5776,12.54533 -7.8906,18.16309 -0.359,6.41731 3.0392,12.85538 9.1914,13.16741 6.1485,0.31185 8.6442,-4.94803 9.6601,-9.28461 1.016,4.33658 3.5117,9.59646 9.6602,9.28461 6.1522,-0.31203 9.5504,-6.7501 9.1914,-13.16741 -0.313,-5.61776 -3.6796,-12.13983 -7.8906,-18.16309 -6.2946,-9.00079 -10.961,-16.65626 -10.961,-16.65626 z"
         id="path10340-9-7"
         transform="translate(3124.62,-853.906)" />
      <g
         style="opacity:1;fill:#d40000;fill-opacity:1;stroke-width:1.00002;stroke-miterlimit:4;stroke-dasharray:none"
         transform="matrix(1,0,0,0.99998,1980.62,-1572.28)"
         id="g7724-24-8-9-7">
        <g
           style="fill:#d40000;fill-opacity:1;stroke-width:1.00002;stroke-miterlimit:4;stroke-dasharray:none"
           id="g8345-9-7-5-1">
          <path
             style="fill:#c8102e;fill-opacity:1;stroke-width:1.00002;stroke-miterlimit:4;stroke-dasharray:none"
             id="path31-8-2-9-8"
             d="m 1038.5828,555.12975 v -33 h 3.1988 v 33 z m 0,0" />
          <path
             style="fill:#c8102e;fill-opacity:1;stroke-width:1.00002;stroke-miterlimit:4;stroke-dasharray:none"
             id="path33-2-3-1-6"
             d="m 1051.2635,522.1291 c -3.0793,0 -5.5542,2.81264 -5.5542,6.30617 v 20.38896 c 0,3.49354 2.4762,6.30487 5.5542,6.30487 h 1.7974 c 3.0792,0 5.5593,-2.81133 5.5593,-6.30487 v -20.38896 c 0,-3.49353 -2.4814,-6.30617 -5.5593,-6.30617 z m 0.9295,3.12035 c 1.818,0 3.2798,1.39453 3.2798,3.13868 v 20.65871 c 0,1.74415 -1.4618,3.15177 -3.2798,3.15177 -1.8179,0 -3.2849,-1.40762 -3.2849,-3.15177 v -20.65871 c 0,-1.74284 1.467,-3.13868 3.2849,-3.13868 z m 0,0" />
        </g>
      </g>
      <g
         style="opacity:1;fill:#d40000;fill-opacity:1"
         transform="matrix(-1,0,0,-0.99998,4261.31,-244.237)"
         id="g7724-2-5-0-2-7">
        <g
           style="fill:#d40000;fill-opacity:1"
           id="g8345-5-0-9-6-9">
          <g
             transform="translate(-5.30767,-0.3292)"
             id="g10963-5-1">
            <path
               d="m 1038.5828,555.12975 v -33 h 3.1988 v 33 z m 0,0"
               id="path31-1-3-2-1-5"
               style="fill:#c8102e;fill-opacity:1" />
            <path
               d="m 1051.2635,522.1291 c -3.0793,0 -5.5542,2.81264 -5.5542,6.30617 v 20.38896 c 0,3.49354 2.4762,6.30487 5.5542,6.30487 h 1.7974 c 3.0792,0 5.5593,-2.81133 5.5593,-6.30487 v -20.38896 c 0,-3.49353 -2.4814,-6.30617 -5.5593,-6.30617 z m 0.9295,3.12035 c 1.818,0 3.2798,1.39453 3.2798,3.13868 v 20.65871 c 0,1.74415 -1.4618,3.15177 -3.2798,3.15177 -1.8179,0 -3.2849,-1.40762 -3.2849,-3.15177 v -20.65871 c 0,-1.74284 1.467,-3.13868 3.2849,-3.13868 z m 0,0"
               id="path33-5-5-3-3-1"
               style="fill:#c8102e;fill-opacity:1" />
          </g>
        </g>
      </g>
    </g>
  </g>
  <g
     transform="translate(-327.70255,170.28142)"
     id="g862-0"
     sodipodi:insensitive="true">
    <rect
       ry="124.68219"
       rx="144.48164"
       y="-1029.6816"
       x="-356.47275"
       height="794.39795"
       width="1659.9126"
       id="rect22845-2-8"
       style="opacity:1;vector-effect:none;fill:#006614;fill-opacity:1;stroke:none;stroke-width:1.74801;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <text
       onclick=""
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:42.6667px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.06667px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       x="472.19449"
       y="-935.81342"
       id="text5630-0-9"><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-935.81342"
         id="tspan4923-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Single Playing Card from:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-875.81342"
         id="tspan5035-8"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Standard Edition - Color Set - Bordered </tspan><tspan
         id="tspan12311-7"
         sodipodi:role="line"
         x="472.19449"
         y="-820.48004" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-762.48004"
         id="tspan4927-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Copyright 2011, 2020 - Chris Aguilar - <EMAIL></tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-707.14667"
         id="tspan12344-3" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-649.14667"
         id="tspan4931-2"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">https://totalnonsense.com/open-source-vector-playing-cards/</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-593.81329"
         id="tspan4929-3" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-535.81329"
         id="tspan4919-5"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Licensed under:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-475.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px"
         id="tspan1594-5-6">LGPL 3.0</tspan><tspan
         id="tspan12253-3-1"
         sodipodi:role="line"
         x="472.19449"
         y="-415.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">https://www.gnu.org/licenses/lgpl-3.0.html</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-360.47995"
         id="tspan5031-6" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-302.47992"
         id="tspan5033-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Each Card Measures (63mm x 88mm)</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-247.14656"
         id="tspan5634-6-0" /></text>
  </g>
</svg>
