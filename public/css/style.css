/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* 区块样式 */
.section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.hidden {
    display: none;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

input[type="text"] {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    margin: 5px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 游戏信息样式 */
.game-info, .room-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.room-info span {
    margin-right: 20px;
    font-weight: bold;
}

/* 玩家列表样式 */
.players-list ul, #admin-players-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.players-list li, #admin-players-list li {
    background: #e9ecef;
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: bold;
}

/* 卡牌样式 */
.card-container {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

/* 层叠式卡牌容器 */
.card-container-stacked {
    position: relative;
    height: 140px;
    margin: 20px 0;
    padding-left: 20px;
}

.card {
    width: 60px;
    height: 84px;
    background: white;
    border: 2px solid #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 层叠式卡牌 */
.card-stacked {
    width: 80px;
    height: 112px;
    background: white;
    border: 2px solid #333;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 3px 3px 12px rgba(0,0,0,0.3);
    position: absolute;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease, z-index 0.3s ease;
    cursor: pointer;
}

.card-stacked:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 5px 5px 20px rgba(0,0,0,0.4);
    z-index: 100 !important;
}

.card.red, .card-stacked.red {
    color: #dc3545;
}

.card.black, .card-stacked.black {
    color: #333;
}

/* 卡牌文字样式（当图片加载失败时使用） */
.card-text {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
}

.card-text.red {
    color: #dc3545;
}

.card-text.black {
    color: #333;
}

/* 结果显示样式 */
.result {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
    font-weight: bold;
    text-align: center;
}

.message {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    pointer-events: none;
}

/* 管理员卡牌区域样式 */
.admin-cards {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid #e9ecef;
}

.admin-cards h3 {
    color: #007bff;
    margin-bottom: 15px;
    font-weight: bold;
}

/* 消息类型样式 */
.message-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.message-error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.message-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.message-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* 游戏结果样式 */
.game-results {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.result-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.result-item .rank {
    font-size: 1.5em;
    font-weight: bold;
    color: #007bff;
    margin-right: 15px;
    min-width: 30px;
}

.result-item .player-name {
    flex: 1;
    font-weight: bold;
}

.result-item .card-type {
    color: #28a745;
    font-weight: bold;
    margin-right: 10px;
}

.result-item .points {
    color: #dc3545;
    font-weight: bold;
}

/* 管理页面特殊样式 */
.player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.player-name.admin {
    color: #007bff;
    font-weight: bold;
}

.player-status {
    font-size: 0.9em;
    padding: 2px 8px;
    border-radius: 12px;
    background: #e9ecef;
}

.admin-badge {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8em;
}

/* 提示框样式 */
.alert {
    padding: 12px 20px;
    border-radius: 8px;
    margin: 10px 0;
    font-weight: bold;
    display: none;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* 服务器状态样式 */
.server-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #dee2e6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.stat-label {
    font-weight: bold;
    color: #6c757d;
}

.stat-value {
    color: #007bff;
    font-weight: bold;
}

/* 响应式设计 */

/* 平板端样式 */
@media (max-width: 1024px) {
    .card-stacked {
        width: 70px;
        height: 98px;
        font-size: 14px;
    }

    .card-container-stacked {
        height: 120px;
    }

    .card-stacked:hover {
        transform: translateY(-8px) scale(1.03);
    }
}

/* 手机端样式 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .section {
        padding: 20px;
    }

    .card {
        width: 50px;
        height: 70px;
        font-size: 12px;
    }

    .card-stacked {
        width: 60px;
        height: 84px;
        font-size: 12px;
        border-radius: 8px;
    }

    .card-container-stacked {
        height: 100px;
        padding-left: 10px;
    }

    .card-stacked:hover {
        transform: translateY(-6px) scale(1.02);
    }

    .card-text {
        font-size: 14px;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .player-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .admin-cards {
        padding: 15px;
        margin: 15px 0;
    }
}

/* 小屏手机端样式 */
@media (max-width: 480px) {
    .card-stacked {
        width: 50px;
        height: 70px;
        font-size: 10px;
        border-radius: 6px;
    }

    .card-container-stacked {
        height: 85px;
        padding-left: 5px;
    }

    .card-stacked:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .card-text {
        font-size: 12px;
    }

    .admin-cards {
        padding: 10px;
        margin: 10px 0;
    }

    .admin-cards h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
}
