<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牛牛游戏 - 管理页面</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎮 牛牛游戏管理</h1>
        </header>

        <main>
            <div id="create-section" class="section">
                <h2>创建游戏房间</h2>
                <button id="create-room-btn" class="btn btn-primary">创建房间</button>
                <button id="admin-reconnect-btn" class="btn btn-secondary hidden">尝试重连</button>
            </div>

            <div id="admin-section" class="section hidden">
                <div class="room-info">
                    <h2>房间信息</h2>
                    <p>房间码: <strong id="room-code-display"></strong></p>
                    <p>玩家数量: <span id="player-count-display">0</span></p>
                </div>

                <div class="players-management">
                    <h3>玩家管理</h3>
                    <ul id="admin-players-list"></ul>
                </div>

                <div class="game-controls">
                    <h3>游戏控制</h3>
                    <button id="start-game-btn" class="btn btn-success">开始游戏</button>
                    <button id="deal-cards-btn" class="btn btn-warning" disabled>重新发牌</button>
                    <button id="end-game-btn" class="btn btn-danger" disabled>结束游戏</button>
                </div>

                <div class="game-results">
                    <h3>游戏结果</h3>
                    <div id="results-display"></div>
                </div>

                <div class="game-history">
                    <h3>游戏历史</h3>
                    <div id="history-display"></div>
                </div>
            </div>
        </main>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
