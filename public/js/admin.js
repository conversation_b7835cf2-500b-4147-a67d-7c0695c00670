// 管理页面逻辑
class NiuniuAdmin {
    constructor() {
        this.socket = io();
        this.roomCode = '';
        this.isRoomCreated = false;
        this.gameState = 'waiting';
        this.players = [];
        this.socketId = null;
        this.adminName = '';

        this.initializeElements();
        this.bindEvents();
        this.setupSocketListeners();
        this.setupReconnectHandler();
        this.loadServerStats();
    }

    initializeElements() {
        // 获取DOM元素
        this.createSection = document.getElementById('create-section');
        this.adminSection = document.getElementById('admin-section');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.roomCodeDisplay = document.getElementById('room-code-display');
        this.playerCountDisplay = document.getElementById('player-count-display');
        this.adminPlayersListUl = document.getElementById('admin-players-list');
        this.startGameBtn = document.getElementById('start-game-btn');
        this.dealCardsBtn = document.getElementById('deal-cards-btn');
        this.endGameBtn = document.getElementById('end-game-btn');
        this.resultsDisplay = document.getElementById('results-display');
        this.historyDisplay = document.getElementById('history-display');
        this.adminReconnectBtn = document.getElementById('admin-reconnect-btn');
    }

    bindEvents() {
        // 绑定事件监听器
        this.createRoomBtn.addEventListener('click', () => this.createRoom());
        this.startGameBtn.addEventListener('click', () => this.startGame());
        this.dealCardsBtn.addEventListener('click', () => this.dealCards());
        this.endGameBtn.addEventListener('click', () => this.endGame());
        this.adminReconnectBtn.addEventListener('click', () => this.manualReconnect());
    }

    setupSocketListeners() {
        // Socket事件监听
        this.socket.on('connect', () => {
            console.log('管理员连接到服务器');
            this.socketId = this.socket.id;

            // 检查是否有需要恢复的连接
            this.attemptReconnect();
        });

        this.socket.on('connected', (data) => {
            console.log('服务器确认连接:', data.socketId);
            this.socketId = data.socketId;
        });

        this.socket.on('disconnect', () => {
            console.log('与服务器断开连接');
            this.showAlert('与服务器断开连接，正在尝试重连...', 'warning');
        });

        this.socket.on('roomCreated', (data) => {
            this.onRoomCreated(data);
        });

        this.socket.on('roomError', (error) => {
            this.showAlert(error.message, 'error');
        });

        // 重连相关事件
        this.socket.on('reconnectSuccess', (data) => {
            console.log('管理员重新连接成功:', data);
            this.showAlert('重新连接成功', 'success');

            // 恢复管理员状态
            this.isRoomCreated = true;
            this.roomCode = data.roomCode;
            this.adminName = data.playerName || this.adminName;
            this.roomCodeDisplay.textContent = data.roomCode;

            // 切换到管理界面
            this.createSection.classList.add('hidden');
            this.adminSection.classList.remove('hidden');

            // 更新玩家列表
            this.updatePlayersList(data.players);

            // 清除重连信息和隐藏重连按钮
            this.clearReconnectInfo();
            this.hideReconnectButton();
        });

        this.socket.on('reconnectFailed', (error) => {
            console.log('管理员重新连接失败:', error.message);
            this.showAlert('重新连接失败: ' + error.message, 'error');

            // 尝试重试重连
            this.retryReconnect();
        });

        this.socket.on('playersUpdate', (players) => {
            this.updatePlayersList(players);
        });

        this.socket.on('gameStart', (data) => {
            this.onGameStarted(data);
        });

        this.socket.on('dealComplete', (data) => {
            this.showAlert(data.message, 'success');
        });

        this.socket.on('gameResults', (results) => {
            this.displayResults(results);
        });

        this.socket.on('gameEnd', (data) => {
            this.onGameEnded(data);
        });

        this.socket.on('gameError', (error) => {
            this.showAlert(error.message, 'error');
        });
    }

    createRoom() {
        const adminName = prompt('请输入管理员名称:', '管理员') || '管理员';
        this.adminName = adminName;
        this.socket.emit('createRoom', { adminName });
    }

    onRoomCreated(data) {
        this.isRoomCreated = true;
        this.roomCode = data.roomCode;
        this.roomCodeDisplay.textContent = data.roomCode;

        // 切换到管理界面
        this.createSection.classList.add('hidden');
        this.adminSection.classList.remove('hidden');

        console.log('房间创建成功:', data.roomCode);
    }

    updatePlayersList(players) {
        this.players = players;
        this.adminPlayersListUl.innerHTML = '';
        this.playerCountDisplay.textContent = players.length;

        players.forEach(player => {
            const li = document.createElement('li');
            li.className = 'player-item';
            li.innerHTML = `
                <span class="player-name ${player.isAdmin ? 'admin' : ''}">${player.name}</span>
                <span class="player-status">${player.isReady ? '✓ 准备' : '⏳ 等待'}</span>
                ${!player.isAdmin ? `<button onclick="window.adminInstance.removePlayer('${player.socketId}')" class="btn btn-danger btn-sm">移除</button>` : '<span class="admin-badge">管理员</span>'}
            `;
            this.adminPlayersListUl.appendChild(li);
        });

        // 更新按钮状态
        this.updateButtonStates();
    }

    removePlayer(playerId) {
        const player = this.players.find(p => p.socketId === playerId);
        const playerName = player ? player.name : '未知玩家';

        if (confirm(`确定要移除玩家 "${playerName}" 吗？`)) {
            this.socket.emit('removePlayer', { playerId });
        }
    }

    updateButtonStates() {
        const playerCount = this.players.length;
        const canStart = playerCount >= 2 && this.gameState === 'waiting';

        this.startGameBtn.disabled = !canStart;
        this.dealCardsBtn.disabled = this.gameState !== 'playing';
        this.endGameBtn.disabled = this.gameState !== 'playing';
    }

    showAlert(message, type = 'info') {
        // 创建或更新提示区域
        let alertDiv = document.getElementById('admin-alert');
        if (!alertDiv) {
            alertDiv = document.createElement('div');
            alertDiv.id = 'admin-alert';
            alertDiv.className = 'alert';
            this.adminSection.insertBefore(alertDiv, this.adminSection.firstChild);
        }

        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        alertDiv.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            alertDiv.style.display = 'none';
        }, 3000);

        console.log(`[ADMIN ${type.toUpperCase()}] ${message}`);
    }

    async loadServerStats() {
        try {
            const response = await fetch('/api/status');
            const result = await response.json();

            if (result.success) {
                console.log('服务器状态:', result.data);
                this.displayServerStats(result.data);
            }
        } catch (error) {
            console.error('加载服务器状态失败:', error);
        }
    }

    displayServerStats(stats) {
        // 创建统计信息显示区域
        let statsDiv = document.getElementById('server-stats');
        if (!statsDiv) {
            statsDiv = document.createElement('div');
            statsDiv.id = 'server-stats';
            statsDiv.className = 'server-stats';
            this.createSection.appendChild(statsDiv);
        }

        const roomStats = stats.stats || {};
        statsDiv.innerHTML = `
            <h4>服务器状态</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">运行时间:</span>
                    <span class="stat-value">${Math.floor(stats.uptime / 60)} 分钟</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总房间数:</span>
                    <span class="stat-value">${roomStats.totalRooms || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">活跃房间:</span>
                    <span class="stat-value">${roomStats.activeRooms || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">在线玩家:</span>
                    <span class="stat-value">${roomStats.totalPlayers || 0}</span>
                </div>
            </div>
        `;
    }

    startGame() {
        if (confirm('确定开始游戏吗？')) {
            this.socket.emit('startGame');
        }
    }

    onGameStarted(data) {
        this.gameState = 'playing';
        this.updateButtonStates();
        this.showAlert('游戏已开始！', 'success');

        console.log('游戏已开始:', data);
    }

    dealCards() {
        if (confirm('确定重新发牌吗？')) {
            this.socket.emit('dealCards');
        }
    }

    displayResults(results) {
        this.resultsDisplay.innerHTML = '<h4>本轮结果</h4>';

        // 按点数排序
        const sortedResults = results.sort((a, b) => b.points - a.points);

        sortedResults.forEach((result, index) => {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <strong>${index + 1}. ${result.playerName}</strong><br>
                牌型: ${result.type} | 点数: ${result.points}<br>
                手牌: ${result.cards.map(card => card.display).join(' ')}
            `;
            this.resultsDisplay.appendChild(resultDiv);
        });

        // 添加到历史记录
        this.addToHistory(sortedResults);
    }

    addToHistory(results) {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.style.marginBottom = '20px';
        historyItem.style.padding = '15px';
        historyItem.style.backgroundColor = '#f8f9fa';
        historyItem.style.borderRadius = '10px';

        const timestamp = new Date().toLocaleString();
        historyItem.innerHTML = `
            <h5>游戏记录 - ${timestamp}</h5>
            <div>获胜者: <strong>${results[0].playerName}</strong> (${results[0].type} - ${results[0].points}点)</div>
        `;

        this.historyDisplay.insertBefore(historyItem, this.historyDisplay.firstChild);
    }

    endGame() {
        if (confirm('确定结束游戏吗？')) {
            this.socket.emit('endGame');
        }
    }

    onGameEnded(data) {
        this.gameState = 'waiting';
        this.updateButtonStates();
        this.showAlert('游戏已结束！', 'info');

        console.log('游戏已结束:', data);
    }

    /**
     * 设置重连处理器
     */
    setupReconnectHandler() {
        // 页面即将刷新或关闭时保存房间信息
        window.addEventListener('beforeunload', () => {
            if (this.isRoomCreated && this.socketId && this.roomCode) {
                // 保存管理员房间信息到sessionStorage和localStorage（双重保险）
                const reconnectInfo = {
                    playerName: this.adminName,
                    roomCode: this.roomCode,
                    oldSocketId: this.socketId,
                    timestamp: Date.now(),
                    retryCount: 0,
                    isAdmin: true  // 标记为管理员
                };

                sessionStorage.setItem('niuniuAdminReconnectInfo', JSON.stringify(reconnectInfo));
                localStorage.setItem('niuniuAdminReconnectInfo', JSON.stringify(reconnectInfo));
            }
        });
    }

    /**
     * 尝试重新连接到房间
     */
    attemptReconnect() {
        // 优先从sessionStorage获取，然后从localStorage获取
        let reconnectInfo = sessionStorage.getItem('niuniuAdminReconnectInfo') ||
                           localStorage.getItem('niuniuAdminReconnectInfo');

        if (reconnectInfo) {
            try {
                const info = JSON.parse(reconnectInfo);
                // 检查信息是否过期（30秒内有效）
                if (Date.now() - info.timestamp < 30000 && info.isAdmin) {
                    console.log('尝试管理员重新连接到房间:', info.roomCode);
                    this.showAlert('正在重新连接到房间...', 'info');

                    // 尝试重新连接到原来的房间
                    this.socket.emit('reconnectRoom', {
                        oldSocketId: info.oldSocketId,
                        playerName: info.playerName,
                        roomCode: info.roomCode,
                        isAdmin: true
                    });
                } else {
                    console.log('管理员重连信息已过期');
                    this.clearReconnectInfo();
                }
            } catch (e) {
                console.error('解析管理员重连信息失败:', e);
                this.clearReconnectInfo();
            }
        }
    }

    /**
     * 重试重新连接
     */
    retryReconnect() {
        let reconnectInfo = sessionStorage.getItem('niuniuAdminReconnectInfo') ||
                           localStorage.getItem('niuniuAdminReconnectInfo');

        if (reconnectInfo) {
            try {
                const info = JSON.parse(reconnectInfo);
                info.retryCount = (info.retryCount || 0) + 1;

                // 最多重试3次
                if (info.retryCount <= 3 && Date.now() - info.timestamp < 30000) {
                    console.log(`管理员重连重试 ${info.retryCount}/3`);
                    this.showAlert(`重连失败，正在重试 (${info.retryCount}/3)...`, 'warning');

                    // 更新重连信息
                    sessionStorage.setItem('niuniuAdminReconnectInfo', JSON.stringify(info));
                    localStorage.setItem('niuniuAdminReconnectInfo', JSON.stringify(info));

                    // 延迟2秒后重试
                    setTimeout(() => {
                        this.socket.emit('reconnectRoom', {
                            oldSocketId: info.oldSocketId,
                            playerName: info.playerName,
                            roomCode: info.roomCode,
                            isAdmin: true
                        });
                    }, 2000);
                } else {
                    console.log('管理员重连重试次数已用完或信息过期');
                    this.showAlert('重新连接失败，房间可能已被删除', 'error');
                    this.showReconnectButton();
                    this.clearReconnectInfo();

                    // 切换回创建房间界面
                    this.isRoomCreated = false;
                    this.createSection.classList.remove('hidden');
                    this.adminSection.classList.add('hidden');
                }
            } catch (e) {
                console.error('管理员重试重连失败:', e);
                this.clearReconnectInfo();
            }
        }
    }

    /**
     * 清除重连信息
     */
    clearReconnectInfo() {
        sessionStorage.removeItem('niuniuAdminReconnectInfo');
        localStorage.removeItem('niuniuAdminReconnectInfo');
    }

    /**
     * 显示重连按钮
     */
    showReconnectButton() {
        if (this.adminReconnectBtn) {
            this.adminReconnectBtn.classList.remove('hidden');
        }
    }

    /**
     * 隐藏重连按钮
     */
    hideReconnectButton() {
        if (this.adminReconnectBtn) {
            this.adminReconnectBtn.classList.add('hidden');
        }
    }

    /**
     * 手动重连
     */
    manualReconnect() {
        console.log('管理员手动触发重连');
        this.hideReconnectButton();
        this.showAlert('正在尝试重新连接...', 'info');

        // 重新尝试连接
        this.attemptReconnect();

        // 如果没有重连信息，提示用户重新创建房间
        setTimeout(() => {
            const reconnectInfo = sessionStorage.getItem('niuniuAdminReconnectInfo') ||
                                 localStorage.getItem('niuniuAdminReconnectInfo');
            if (!reconnectInfo) {
                this.showAlert('没有找到重连信息，请重新创建房间', 'warning');
            }
        }, 3000);
    }
}

// 初始化管理页面
document.addEventListener('DOMContentLoaded', () => {
    window.adminInstance = new NiuniuAdmin();
});
