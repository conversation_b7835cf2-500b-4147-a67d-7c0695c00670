// 管理页面逻辑
class NiuniuAdmin {
    constructor() {
        this.socket = io();
        this.roomCode = '';
        this.isRoomCreated = false;
        this.gameState = 'waiting';
        this.players = [];

        this.initializeElements();
        this.bindEvents();
        this.setupSocketListeners();
        this.loadServerStats();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.createSection = document.getElementById('create-section');
        this.adminSection = document.getElementById('admin-section');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.roomCodeDisplay = document.getElementById('room-code-display');
        this.playerCountDisplay = document.getElementById('player-count-display');
        this.adminPlayersListUl = document.getElementById('admin-players-list');
        this.startGameBtn = document.getElementById('start-game-btn');
        this.dealCardsBtn = document.getElementById('deal-cards-btn');
        this.endGameBtn = document.getElementById('end-game-btn');
        this.resultsDisplay = document.getElementById('results-display');
        this.historyDisplay = document.getElementById('history-display');
    }
    
    bindEvents() {
        // 绑定事件监听器
        this.createRoomBtn.addEventListener('click', () => this.createRoom());
        this.startGameBtn.addEventListener('click', () => this.startGame());
        this.dealCardsBtn.addEventListener('click', () => this.dealCards());
        this.endGameBtn.addEventListener('click', () => this.endGame());
    }
    
    setupSocketListeners() {
        // Socket事件监听
        this.socket.on('connect', () => {
            console.log('管理员连接到服务器');
        });

        this.socket.on('connected', (data) => {
            console.log('服务器确认连接:', data.socketId);
        });

        this.socket.on('disconnect', () => {
            console.log('与服务器断开连接');
            this.showAlert('与服务器断开连接，请刷新页面重试', 'error');
        });

        this.socket.on('roomCreated', (data) => {
            this.onRoomCreated(data);
        });

        this.socket.on('roomError', (error) => {
            this.showAlert(error.message, 'error');
        });

        this.socket.on('playersUpdate', (players) => {
            this.updatePlayersList(players);
        });

        this.socket.on('gameStart', (data) => {
            this.onGameStarted(data);
        });

        this.socket.on('dealComplete', (data) => {
            this.showAlert(data.message, 'success');
        });

        this.socket.on('gameResults', (results) => {
            this.displayResults(results);
        });

        this.socket.on('gameEnd', (data) => {
            this.onGameEnded(data);
        });

        this.socket.on('gameError', (error) => {
            this.showAlert(error.message, 'error');
        });
    }
    
    createRoom() {
        const adminName = prompt('请输入管理员名称:', '管理员') || '管理员';
        this.socket.emit('createRoom', { adminName });
    }
    
    onRoomCreated(data) {
        this.isRoomCreated = true;
        this.roomCode = data.roomCode;
        this.roomCodeDisplay.textContent = data.roomCode;
        
        // 切换到管理界面
        this.createSection.classList.add('hidden');
        this.adminSection.classList.remove('hidden');
        
        console.log('房间创建成功:', data.roomCode);
    }
    
    updatePlayersList(players) {
        this.players = players;
        this.adminPlayersListUl.innerHTML = '';
        this.playerCountDisplay.textContent = players.length;

        players.forEach(player => {
            const li = document.createElement('li');
            li.className = 'player-item';
            li.innerHTML = `
                <span class="player-name ${player.isAdmin ? 'admin' : ''}">${player.name}</span>
                <span class="player-status">${player.isReady ? '✓ 准备' : '⏳ 等待'}</span>
                ${!player.isAdmin ? `<button onclick="window.adminInstance.removePlayer('${player.socketId}')" class="btn btn-danger btn-sm">移除</button>` : '<span class="admin-badge">管理员</span>'}
            `;
            this.adminPlayersListUl.appendChild(li);
        });

        // 更新按钮状态
        this.updateButtonStates();
    }
    
    removePlayer(playerId) {
        const player = this.players.find(p => p.socketId === playerId);
        const playerName = player ? player.name : '未知玩家';

        if (confirm(`确定要移除玩家 "${playerName}" 吗？`)) {
            this.socket.emit('removePlayer', { playerId });
        }
    }

    updateButtonStates() {
        const playerCount = this.players.length;
        const canStart = playerCount >= 2 && this.gameState === 'waiting';

        this.startGameBtn.disabled = !canStart;
        this.dealCardsBtn.disabled = this.gameState !== 'playing';
        this.endGameBtn.disabled = this.gameState !== 'playing';
    }

    showAlert(message, type = 'info') {
        // 创建或更新提示区域
        let alertDiv = document.getElementById('admin-alert');
        if (!alertDiv) {
            alertDiv = document.createElement('div');
            alertDiv.id = 'admin-alert';
            alertDiv.className = 'alert';
            this.adminSection.insertBefore(alertDiv, this.adminSection.firstChild);
        }

        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        alertDiv.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            alertDiv.style.display = 'none';
        }, 3000);

        console.log(`[ADMIN ${type.toUpperCase()}] ${message}`);
    }

    async loadServerStats() {
        try {
            const response = await fetch('/api/status');
            const result = await response.json();

            if (result.success) {
                console.log('服务器状态:', result.data);
                this.displayServerStats(result.data);
            }
        } catch (error) {
            console.error('加载服务器状态失败:', error);
        }
    }

    displayServerStats(stats) {
        // 创建统计信息显示区域
        let statsDiv = document.getElementById('server-stats');
        if (!statsDiv) {
            statsDiv = document.createElement('div');
            statsDiv.id = 'server-stats';
            statsDiv.className = 'server-stats';
            this.createSection.appendChild(statsDiv);
        }

        const roomStats = stats.stats || {};
        statsDiv.innerHTML = `
            <h4>服务器状态</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">运行时间:</span>
                    <span class="stat-value">${Math.floor(stats.uptime / 60)} 分钟</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总房间数:</span>
                    <span class="stat-value">${roomStats.totalRooms || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">活跃房间:</span>
                    <span class="stat-value">${roomStats.activeRooms || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">在线玩家:</span>
                    <span class="stat-value">${roomStats.totalPlayers || 0}</span>
                </div>
            </div>
        `;
    }
    
    startGame() {
        if (confirm('确定开始游戏吗？')) {
            this.socket.emit('startGame');
        }
    }
    
    onGameStarted(data) {
        this.gameState = 'playing';
        this.updateButtonStates();
        this.showAlert('游戏已开始！', 'success');

        console.log('游戏已开始:', data);
    }
    
    dealCards() {
        if (confirm('确定重新发牌吗？')) {
            this.socket.emit('dealCards');
        }
    }
    
    displayResults(results) {
        this.resultsDisplay.innerHTML = '<h4>本轮结果</h4>';
        
        // 按点数排序
        const sortedResults = results.sort((a, b) => b.points - a.points);
        
        sortedResults.forEach((result, index) => {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <strong>${index + 1}. ${result.playerName}</strong><br>
                牌型: ${result.type} | 点数: ${result.points}<br>
                手牌: ${result.cards.map(card => card.display).join(' ')}
            `;
            this.resultsDisplay.appendChild(resultDiv);
        });
        
        // 添加到历史记录
        this.addToHistory(sortedResults);
    }
    
    addToHistory(results) {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.style.marginBottom = '20px';
        historyItem.style.padding = '15px';
        historyItem.style.backgroundColor = '#f8f9fa';
        historyItem.style.borderRadius = '10px';
        
        const timestamp = new Date().toLocaleString();
        historyItem.innerHTML = `
            <h5>游戏记录 - ${timestamp}</h5>
            <div>获胜者: <strong>${results[0].playerName}</strong> (${results[0].type} - ${results[0].points}点)</div>
        `;
        
        this.historyDisplay.insertBefore(historyItem, this.historyDisplay.firstChild);
    }
    
    endGame() {
        if (confirm('确定结束游戏吗？')) {
            this.socket.emit('endGame');
        }
    }
    
    onGameEnded(data) {
        this.gameState = 'waiting';
        this.updateButtonStates();
        this.showAlert('游戏已结束！', 'info');

        console.log('游戏已结束:', data);
    }
}

// 初始化管理页面
document.addEventListener('DOMContentLoaded', () => {
    window.adminInstance = new NiuniuAdmin();
});
