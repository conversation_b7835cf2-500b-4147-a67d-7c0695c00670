// 游戏客户端逻辑
class NiuniuGame {
    constructor() {
        this.socket = io();
        this.playerName = '';
        this.roomCode = '';
        this.isInGame = false;
        this.isReady = false;
        this.gameState = 'waiting';
        this.socketId = null;
        this.oldSocketId = null;

        this.initializeElements();
        this.bindEvents();
        this.setupSocketListeners();
        this.loadGameRules();
        this.setupReconnectHandler();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.joinSection = document.getElementById('join-section');
        this.gameSection = document.getElementById('game-section');
        this.playerNameInput = document.getElementById('player-name');
        this.roomCodeInput = document.getElementById('room-code');
        this.joinBtn = document.getElementById('join-btn');
        this.currentRoomSpan = document.getElementById('current-room');
        this.playerCountSpan = document.getElementById('player-count');
        this.playersUl = document.getElementById('players');
        this.myHandDiv = document.getElementById('my-hand');
        this.myResultDiv = document.getElementById('my-result');
        this.gameMessageDiv = document.getElementById('game-message');
        this.reconnectBtn = document.getElementById('reconnect-btn');
    }
    
    bindEvents() {
        // 绑定事件监听器
        this.joinBtn.addEventListener('click', () => this.joinGame());
        
        // 回车键加入游戏
        this.playerNameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.joinGame();
        });
        
        this.roomCodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.joinGame();
        });

        // 手动重连按钮
        this.reconnectBtn.addEventListener('click', () => this.manualReconnect());
    }
    
    setupReconnectHandler() {
        // 页面即将刷新或关闭时保存房间信息
        window.addEventListener('beforeunload', () => {
            if (this.isInGame && this.socketId) {
                // 保存房间信息到sessionStorage和localStorage（双重保险）
                const reconnectInfo = {
                    playerName: this.playerName,
                    roomCode: this.roomCode,
                    oldSocketId: this.socketId,
                    timestamp: Date.now(),
                    retryCount: 0
                };

                sessionStorage.setItem('niuniuGameReconnectInfo', JSON.stringify(reconnectInfo));
                localStorage.setItem('niuniuGameReconnectInfo', JSON.stringify(reconnectInfo));
            }
        });
    }

    setupSocketListeners() {
        // Socket事件监听
        this.socket.on('connect', () => {
            console.log('连接到服务器');
            this.showMessage('已连接到服务器');

            // 保存当前socket ID
            this.socketId = this.socket.id;

            // 检查是否有需要恢复的连接
            this.attemptReconnect();
        });

        this.socket.on('connected', (data) => {
            console.log('服务器确认连接:', data.socketId);
            this.socketId = data.socketId;
        });

        this.socket.on('reconnectSuccess', (data) => {
            console.log('重新连接成功:', data);
            this.showMessage('重新连接成功', 'success');

            // 恢复游戏状态
            this.isInGame = true;
            this.roomCode = data.roomCode;
            this.playerName = data.playerName || this.playerName;
            this.currentRoomSpan.textContent = data.roomCode;

            // 切换到游戏界面
            this.joinSection.classList.add('hidden');
            this.gameSection.classList.remove('hidden');

            // 更新玩家列表
            this.updatePlayersList(data.players);

            // 清除重连信息和隐藏重连按钮
            this.clearReconnectInfo();
            this.hideReconnectButton();
        });

        this.socket.on('reconnectFailed', (error) => {
            console.log('重新连接失败:', error.message);
            this.showMessage('重新连接失败: ' + error.message, 'error');

            // 尝试重试重连
            this.retryReconnect();
        });

        this.socket.on('disconnect', () => {
            console.log('与服务器断开连接');
            this.showMessage('与服务器断开连接，正在尝试重连...', 'warning');
        });

        this.socket.on('joinSuccess', (data) => {
            this.onJoinSuccess(data);
        });

        this.socket.on('joinError', (error) => {
            this.showMessage(error.message, 'error');
        });

        this.socket.on('playersUpdate', (players) => {
            this.updatePlayersList(players);
        });

        this.socket.on('gameStart', (data) => {
            this.onGameStart(data);
        });

        this.socket.on('cardsDealt', (data) => {
            this.onCardsDealt(data);
        });

        this.socket.on('dealComplete', (data) => {
            this.showMessage(data.message);
        });

        this.socket.on('gameResults', (results) => {
            this.onGameResults(results);
        });

        this.socket.on('gameEnd', (data) => {
            this.onGameEnd(data);
        });

        this.socket.on('gameError', (error) => {
            this.showMessage(error.message, 'error');
        });

        this.socket.on('removedFromRoom', (data) => {
            this.showMessage(data.message, 'warning');
            this.leaveGame();
        });

        this.socket.on('playerDisconnected', (data) => {
            this.showMessage(data.message, 'info');
        });
    }
    
    async joinGame() {
        const playerName = this.playerNameInput.value.trim();
        const roomCode = this.roomCodeInput.value.trim();

        if (!playerName) {
            this.showMessage('请输入您的昵称', 'error');
            return;
        }

        if (!roomCode) {
            this.showMessage('请输入房间码', 'error');
            return;
        }

        // 验证房间码格式
        if (!/^\d{6}$/.test(roomCode)) {
            this.showMessage('房间码必须是6位数字', 'error');
            return;
        }

        // 先验证房间是否存在
        try {
            const response = await fetch('/api/rooms/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ roomCode })
            });

            const result = await response.json();

            if (!result.success || !result.data.exists) {
                this.showMessage('房间不存在', 'error');
                return;
            }

            if (!result.data.canJoin) {
                this.showMessage('房间已满或游戏进行中，无法加入', 'error');
                return;
            }
        } catch (error) {
            console.error('验证房间失败:', error);
            this.showMessage('验证房间失败，请重试', 'error');
            return;
        }

        this.playerName = playerName;
        this.roomCode = roomCode;

        // 发送加入游戏请求
        this.socket.emit('joinGame', {
            playerName: playerName,
            roomCode: roomCode
        });

        this.showMessage('正在加入房间...', 'info');
    }
    
    onJoinSuccess(data) {
        this.isInGame = true;
        this.roomCode = data.roomCode;
        this.currentRoomSpan.textContent = data.roomCode;

        // 切换到游戏界面
        this.joinSection.classList.add('hidden');
        this.gameSection.classList.remove('hidden');

        this.showMessage('成功加入游戏！等待其他玩家...', 'success');

        // 更新玩家列表
        this.updatePlayersList(data.players);
    }
    
    updatePlayersList(players) {
        this.playersUl.innerHTML = '';
        this.playerCountSpan.textContent = players.length;
        
        players.forEach(player => {
            const li = document.createElement('li');
            li.textContent = player.name;
            if (player.name === this.playerName) {
                li.style.backgroundColor = '#007bff';
                li.style.color = 'white';
            }
            this.playersUl.appendChild(li);
        });
    }
    
    onGameStart(data) {
        this.gameState = 'playing';
        this.showMessage('游戏开始！等待发牌...', 'info');
        this.myHandDiv.innerHTML = '';
        this.myResultDiv.innerHTML = '';
    }

    onCardsDealt(data) {
        this.displayCards(data.cards);
        this.displayResult(data.result);
        this.showMessage('发牌完成！查看您的手牌', 'success');
    }

    onGameResults(results) {
        this.showMessage('游戏结束！查看结果', 'info');
        this.displayGameResults(results);
    }

    onGameEnd(data) {
        this.gameState = 'waiting';
        this.showMessage('游戏结束，可以开始新一轮', 'info');
    }
    
    displayCards(cards) {
        this.myHandDiv.innerHTML = '';

        cards.forEach((card, index) => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'card-stacked';
            cardDiv.style.zIndex = index + 1;
            cardDiv.style.left = `${index * 15}px`; // 层叠偏移

            // 创建图片元素
            const img = document.createElement('img');
            // 转换花色名称为大写并构造图片路径
            const suitMap = {
                'hearts': 'HEART',
                'diamonds': 'DIAMOND',
                'clubs': 'CLUB',
                'spades': 'SPADE'
            };

            const suit = suitMap[card.suit];
            const rank = card.rank === 1 ? '1' :
                         card.rank === 11 ? 'J' :
                         card.rank === 12 ? 'Q' :
                         card.rank === 13 ? 'K' :
                         card.rank.toString();

            img.src = `/assets/images/${suit}-${rank}.svg`;
            img.alt = card.display;
            img.className = 'card-image';
            img.onerror = () => {
                // 如果图片加载失败，显示文字
                cardDiv.innerHTML = `<span class="card-text ${card.color}">${card.display}</span>`;
            };

            cardDiv.appendChild(img);
            this.myHandDiv.appendChild(cardDiv);
        });
    }
    
    displayResult(result) {
        // 牛牛特殊处理，显示为10点而不是20点
        const displayPoints = result.type === '牛牛' ? 10 : result.points;
        
        this.myResultDiv.innerHTML = `
            <div class="result">
                <div>牌型: ${result.type}</div>
                <div>点数: ${displayPoints}</div>
            </div>
        `;
    }

    onGameEnd(results) {
        this.showMessage('游戏结束！查看结果');
        // 这里可以显示所有玩家的结果
        console.log('游戏结果:', results);
    }
    
    showMessage(message, type = 'info') {
        this.gameMessageDiv.textContent = message;

        // 移除之前的类型类
        this.gameMessageDiv.classList.remove('message-success', 'message-error', 'message-warning', 'message-info');

        // 添加新的类型类
        this.gameMessageDiv.classList.add(`message-${type}`);

        // 如果是错误信息，弹出提示框
        if (type === 'error') {
            alert(`错误: ${message}`);
        }

        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    displayGameResults(results) {
        // 创建结果显示区域
        let resultsDiv = document.getElementById('game-results');
        if (!resultsDiv) {
            resultsDiv = document.createElement('div');
            resultsDiv.id = 'game-results';
            resultsDiv.className = 'game-results';
            this.gameSection.appendChild(resultsDiv);
        }

        resultsDiv.innerHTML = '<h3>游戏结果</h3>';

        results.forEach((result, index) => {
            // 牛牛特殊处理，显示为10点而不是20点
            const displayPoints = result.type === '牛牛' ? 10 : result.points;
            
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div class="rank">${index + 1}</div>
                <div class="player-name">${result.playerName}</div>
                <div class="card-type">${result.type}</div>
                <div class="points">${displayPoints}点</div>
            `;
            resultsDiv.appendChild(resultItem);
        });
    }

    leaveGame() {
        if (this.isInGame) {
            this.socket.emit('leaveRoom');
        }

        // 重置状态
        this.isInGame = false;
        this.isReady = false;
        this.gameState = 'waiting';
        this.playerName = '';
        this.roomCode = '';
        this.socketId = null;

        // 切换回加入界面
        this.gameSection.classList.add('hidden');
        this.joinSection.classList.remove('hidden');

        // 清空输入框
        this.playerNameInput.value = '';
        this.roomCodeInput.value = '';

        // 清空游戏区域
        this.myHandDiv.innerHTML = '';
        this.myResultDiv.innerHTML = '';
        this.playersUl.innerHTML = '';

        const resultsDiv = document.getElementById('game-results');
        if (resultsDiv) {
            resultsDiv.remove();
        }

        this.showMessage('已离开游戏', 'info');
    }

    async loadGameRules() {
        try {
            const response = await fetch('/api/game/rules');
            const result = await response.json();

            if (result.success) {
                console.log('游戏规则加载成功:', result.data);
                // 可以在这里显示游戏规则
            }
        } catch (error) {
            console.error('加载游戏规则失败:', error);
        }
    }

    /**
     * 尝试重新连接到房间
     */
    attemptReconnect() {
        // 优先从sessionStorage获取，然后从localStorage获取
        let reconnectInfo = sessionStorage.getItem('niuniuGameReconnectInfo') ||
                           localStorage.getItem('niuniuGameReconnectInfo');

        if (reconnectInfo) {
            try {
                const info = JSON.parse(reconnectInfo);
                // 检查信息是否过期（30秒内有效）
                if (Date.now() - info.timestamp < 30000) {
                    console.log('尝试重新连接到房间:', info.roomCode);
                    this.showMessage('正在重新连接到房间...', 'info');

                    // 尝试重新连接到原来的房间
                    this.socket.emit('reconnectRoom', {
                        oldSocketId: info.oldSocketId,
                        playerName: info.playerName,
                        roomCode: info.roomCode
                    });
                } else {
                    console.log('重连信息已过期');
                    this.clearReconnectInfo();
                }
            } catch (e) {
                console.error('解析重连信息失败:', e);
                this.clearReconnectInfo();
            }
        }
    }

    /**
     * 重试重新连接
     */
    retryReconnect() {
        let reconnectInfo = sessionStorage.getItem('niuniuGameReconnectInfo') ||
                           localStorage.getItem('niuniuGameReconnectInfo');

        if (reconnectInfo) {
            try {
                const info = JSON.parse(reconnectInfo);
                info.retryCount = (info.retryCount || 0) + 1;

                // 最多重试3次
                if (info.retryCount <= 3 && Date.now() - info.timestamp < 30000) {
                    console.log(`重连重试 ${info.retryCount}/3`);
                    this.showMessage(`重连失败，正在重试 (${info.retryCount}/3)...`, 'warning');

                    // 更新重连信息
                    sessionStorage.setItem('niuniuGameReconnectInfo', JSON.stringify(info));
                    localStorage.setItem('niuniuGameReconnectInfo', JSON.stringify(info));

                    // 延迟2秒后重试
                    setTimeout(() => {
                        this.socket.emit('reconnectRoom', {
                            oldSocketId: info.oldSocketId,
                            playerName: info.playerName,
                            roomCode: info.roomCode
                        });
                    }, 2000);
                } else {
                    console.log('重连重试次数已用完或信息过期');
                    this.showMessage('重新连接失败，请手动重新加入房间', 'error');
                    this.showReconnectButton();
                    this.clearReconnectInfo();
                }
            } catch (e) {
                console.error('重试重连失败:', e);
                this.clearReconnectInfo();
            }
        }
    }

    /**
     * 清除重连信息
     */
    clearReconnectInfo() {
        sessionStorage.removeItem('niuniuGameReconnectInfo');
        localStorage.removeItem('niuniuGameReconnectInfo');
    }

    /**
     * 显示重连按钮
     */
    showReconnectButton() {
        if (this.reconnectBtn) {
            this.reconnectBtn.classList.remove('hidden');
        }
    }

    /**
     * 隐藏重连按钮
     */
    hideReconnectButton() {
        if (this.reconnectBtn) {
            this.reconnectBtn.classList.add('hidden');
        }
    }

    /**
     * 手动重连
     */
    manualReconnect() {
        console.log('用户手动触发重连');
        this.hideReconnectButton();
        this.showMessage('正在尝试重新连接...', 'info');

        // 重新尝试连接
        this.attemptReconnect();

        // 如果没有重连信息，提示用户重新加入
        setTimeout(() => {
            const reconnectInfo = sessionStorage.getItem('niuniuGameReconnectInfo') ||
                                 localStorage.getItem('niuniuGameReconnectInfo');
            if (!reconnectInfo) {
                this.showMessage('没有找到重连信息，请重新加入房间', 'warning');
                // 切换回加入界面
                this.isInGame = false;
                this.joinSection.classList.remove('hidden');
                this.gameSection.classList.add('hidden');
            }
        }, 3000);
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new NiuniuGame();
});
