# 在线牛牛游戏 - 产品需求文档

## 概述
开发一个基于Node.js和前端技术的在线牛牛游戏平台，主要用于线下朋友聚会时方便发牌和游戏管理。系统支持多人同时加入游戏，提供简洁易用的游戏界面和管理功能。

## 核心功能

### 游戏功能
- **自动发牌系统**：系统自动为每位玩家发5张牌
- **牛牛计算**：自动计算每手牌的牛牛点数和牌型
- **多人游戏支持**：支持3-8人同时游戏
- **实时同步**：所有玩家状态实时同步显示
- **游戏轮次管理**：支持多轮游戏，记录每轮结果

### 管理功能
- **房间创建**：管理员可以创建游戏房间
- **玩家管理**：添加/移除玩家，设置玩家昵称
- **游戏控制**：开始游戏、重新发牌、结束游戏
- **结果查看**：查看当前轮和历史轮次结果

## 用户体验

### 用户角色
- **管理员**：创建房间、管理游戏流程的主持人
- **玩家**：参与游戏的普通用户

### 核心用户流程
1. 管理员创建游戏房间，获得房间码
2. 玩家通过房间码加入游戏
3. 管理员开始游戏，系统自动发牌
4. 玩家查看自己的牌，系统显示牛牛结果
5. 管理员可以开始下一轮或结束游戏

### 界面设计要求
- **响应式设计**：支持手机和平板设备
- **简洁直观**：适合聚会环境快速上手
- **大字体显示**：方便在聚会环境中查看
- **实时反馈**：操作结果即时显示

## 技术架构

### 前端技术栈
- HTML5/CSS3/JavaScript
- WebSocket客户端（实时通信）
- 响应式CSS框架（如Bootstrap或自定义）
- 可选：Vue.js或React（简化状态管理）

### 后端技术栈
- Node.js + Express.js
- WebSocket服务器（Socket.io）
- 内存数据存储（Redis可选）
- 简单的JSON文件存储（房间和游戏数据）

### 系统组件
- **Web服务器**：提供静态文件和API接口
- **WebSocket服务器**：处理实时通信
- **游戏引擎**：牛牛游戏逻辑处理
- **房间管理器**：管理游戏房间和玩家
- **牌库系统**：扑克牌生成和发牌逻辑

## 开发路线图

### 第一阶段：MVP核心功能
- 基础项目结构搭建
- 简单的房间创建和加入功能
- 基础的发牌和牛牛计算逻辑
- 简单的游戏界面和管理界面
- WebSocket实时通信基础框架

### 第二阶段：完善游戏体验
- 完整的游戏流程实现
- 美化界面设计和用户体验
- 添加游戏历史记录功能
- 错误处理和异常情况处理
- 移动端适配优化

### 第三阶段：增强功能
- 游戏统计和排行榜
- 自定义游戏规则设置
- 音效和动画效果
- 游戏回放功能
- 性能优化和稳定性提升

## 逻辑依赖链

### 开发优先级
1. **基础架构**：项目结构、服务器搭建、WebSocket通信
2. **核心游戏逻辑**：牌库系统、发牌逻辑、牛牛计算
3. **房间管理**：房间创建、玩家加入、状态同步
4. **用户界面**：游戏页面、管理页面、基础交互
5. **游戏流程**：完整的游戏轮次管理
6. **优化完善**：错误处理、界面美化、移动端适配

### 技术实现顺序
- 先实现后端API和WebSocket服务
- 再开发前端界面和交互逻辑
- 最后进行整合测试和优化

## 风险和缓解措施

### 技术挑战
- **实时同步复杂性**：使用成熟的Socket.io库，简化WebSocket实现
- **移动端兼容性**：采用响应式设计，重点测试主流移动浏览器
- **并发处理**：合理设计房间隔离，避免数据冲突

### MVP范围控制
- 专注核心发牌功能，避免过度设计
- 使用内存存储，避免数据库复杂性
- 简化UI设计，重点保证功能可用性

### 资源约束
- 使用轻量级技术栈，降低部署复杂度
- 优先实现核心功能，后续迭代增强体验
- 充分利用开源库，减少重复开发

## 附录

### 牛牛游戏规则
- 每人发5张牌
- 前3张牌组合成10的倍数为"有牛"
- 后2张牌点数相加的个位数为"牛数"
- 特殊牌型：牛牛（后两张也是10的倍数）、五小牛、炸弹等

### 技术规范
- 使用ES6+语法
- 代码注释和文档完整
- 遵循RESTful API设计原则
- WebSocket消息格式统一JSON结构