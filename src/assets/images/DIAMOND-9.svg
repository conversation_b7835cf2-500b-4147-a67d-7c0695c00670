<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg8"
   version="1.1"
   viewBox="0 0 261.11999 355.20002"
   height="93.980003mm"
   width="69.087997mm"
   sodipodi:docname="DIAMOND-9.svg"
   inkscape:version="1.1-dev (60faa23, 2020-06-06)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="774"
     inkscape:window-height="485"
     id="namedview22"
     showgrid="false"
     inkscape:zoom="0.28299805"
     inkscape:cx="133.57097"
     inkscape:cy="283.87406"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="0"
     inkscape:current-layer="svg8" />
  <title
     id="title8745">Vector Playing Cards</title>
  <defs
     id="defs2" />
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Vector Playing Cards</dc:title>
        <dc:creator>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:rights>
          <cc:Agent>
            <dc:title>Copyright 2011, 017</dc:title>
          </cc:Agent>
        </dc:rights>
        <dc:publisher>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:description>Vector Playing Cards</dc:description>
        <dc:contributor>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:contributor>
        <cc:license
           rdf:resource="https://opensource.org/licenses/lgpl-3.0.html" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     transform="matrix(1.0966315,0,0,1.0679512,-974.11977,-1148.726)"
     id="layer1">
    <g
       id="DIAMOND-9"
       transform="matrix(1.06667,0,0,1.06667,-1860.35,1108.11)">
      <rect
         ry="10.66401"
         rx="10.677597"
         style="display:inline;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.00057;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect6962-4"
         width="223.22835"
         height="311.81104"
         x="2576.8359"
         y="-30.444927" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 2594.0533,39.528358 c -2.1609,-3.568597 -4.4344,-7.037098 -7.2754,-9.999997 2.841,-2.962801 5.1145,-6.4313 7.2754,-10 2.1609,3.5687 4.4343,7.037199 7.2754,10 -2.8411,2.962899 -5.1145,6.4314 -7.2754,9.999997 z"
         id="path6994-9" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 2740.2166,50.24996 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         id="path6996-5" />
      <path
         id="path6998-7"
         d="m 2782.8478,231.393 c -2.1609,-3.5686 -4.4344,-7.0371 -7.2754,-10 2.841,-2.9628 5.1145,-6.4313 7.2754,-10 2.1609,3.5687 4.4343,7.0372 7.2754,10 -2.8411,2.9629 -5.1145,6.4314 -7.2754,10 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 2636.6838,50.24996 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         id="path7000-7" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 2740.2166,116.39745 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24.000002 5.1862,8.564902 10.6424,16.889202 17.4609,24.000002 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         id="path7002-0" />
      <path
         id="path7004-0"
         d="m 2636.6838,116.39745 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-23.999998 5.1862,8.564898 10.6424,16.889198 17.4609,23.999998 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         id="path7008-2"
         d="m 2688.4502,149.46061 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         id="path7016-2"
         d="m 2636.6838,248.67634 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         id="path7018-0"
         d="m 2740.2166,248.68328 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         id="path7020-7"
         d="m 2740.2166,182.52946 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1" />
      <path
         style="display:inline;opacity:1;fill:#c8102e;fill-opacity:1"
         d="m 2636.6838,182.49729 c -5.1862,-8.5648 -10.6425,-16.8891 -17.461,-24 6.8185,-7.1108 12.2748,-15.4351 17.461,-24 5.1862,8.5649 10.6424,16.8892 17.4609,24 -6.8185,7.1109 -12.2747,15.4352 -17.4609,24 z"
         id="path7022-4" />
      <path
         style="opacity:1;fill:#c8102e;fill-opacity:1"
         id="path29-5-3-7-8"
         d="m 2584.8335,10.0313 3.0188,-0.9571 c 1.1404,3.043 3.4405,3.9439 5.8756,3.7488 4.3688,-0.3483 5.8203,-5.2324 6.1469,-9.5495 -1.7023,1.8057 -4.2595,2.7223 -6.4387,2.6293 -6.9659,-0.3732 -8.7491,-5.9486 -8.8134,-11.7846 -0.1903,-4.9207 1.8437,-8.4849 5.333,-10.1217 1.8488,-0.8288 6.0813,-0.8288 7.9404,0.1532 4.2685,2.2522 5.8512,6.1424 5.5645,15.4313 0,7.6417 -1.6226,16.5417 -9.8137,16.7355 -4.73,0.6547 -8.3261,-3.0273 -8.8134,-6.2852 z m 11.8579,-8.3736 c 1.4207,-0.8995 2.2204,-1.7441 2.8581,-3.0077 0.522,-1.0383 0.7033,-1.5804 0.5528,-3.7278 -0.387,-5.5401 -3.29,-8.6853 -7.5032,-8.062 -2.3503,0.3535 -3.6359,1.6682 -4.3739,4.1128 -1.3616,4.5214 0.3664,9.81 3.6012,11.0278 1.4554,0.5525 3.7053,0.3941 4.865,-0.3431 z m 0,0" />
      <path
         style="opacity:1;fill:#c8102e;fill-opacity:1"
         id="path29-6-2-9-2"
         d="m 2792.0646,240.8899 -3.0188,0.9572 c -1.1404,-3.043 -3.4405,-3.9439 -5.8756,-3.7488 -4.3688,0.3483 -5.8203,5.2324 -6.1469,9.5495 1.7023,-1.8057 4.2595,-2.7223 6.4387,-2.6293 6.9659,0.3732 8.7491,5.9486 8.8134,11.7846 0.1903,4.9207 -1.8437,8.4849 -5.333,10.1217 -1.8488,0.8288 -6.0813,0.8288 -7.9404,-0.1532 -4.2684,-2.2522 -5.8511,-6.1424 -5.5644,-15.4313 0,-7.6417 1.6225,-16.5417 9.8136,-16.7355 4.73,-0.6547 8.3261,3.0273 8.8134,6.2851 z m -11.8579,8.3737 c -1.4207,0.8995 -2.2204,1.7441 -2.8581,3.0077 -0.522,1.0383 -0.7032,1.5804 -0.5528,3.7278 0.387,5.5401 3.29,8.6853 7.5032,8.062 2.3503,-0.3535 3.6359,-1.6682 4.3739,-4.1128 1.3616,-4.5214 -0.3664,-9.8101 -3.6012,-11.0278 -1.4554,-0.5526 -3.7053,-0.3941 -4.865,0.3431 z m 0,0" />
    </g>
  </g>
  <g
     transform="translate(-345.97676,163.21417)"
     id="g862-0"
     sodipodi:insensitive="true">
    <rect
       ry="124.68219"
       rx="144.48164"
       y="-1029.6816"
       x="-356.47275"
       height="794.39795"
       width="1659.9126"
       id="rect22845-2-8"
       style="opacity:1;vector-effect:none;fill:#006614;fill-opacity:1;stroke:none;stroke-width:1.74801;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <text
       onclick=""
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:42.6667px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.06667px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       x="472.19449"
       y="-935.81342"
       id="text5630-0-9"><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-935.81342"
         id="tspan4923-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Single Playing Card from:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-875.81342"
         id="tspan5035-8"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Standard Edition - Color Set - Bordered </tspan><tspan
         id="tspan12311-7"
         sodipodi:role="line"
         x="472.19449"
         y="-820.48004" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-762.48004"
         id="tspan4927-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Copyright 2011, 2020 - Chris Aguilar - <EMAIL></tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-707.14667"
         id="tspan12344-3" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-649.14667"
         id="tspan4931-2"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">https://totalnonsense.com/open-source-vector-playing-cards/</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-593.81329"
         id="tspan4929-3" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-535.81329"
         id="tspan4919-5"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Licensed under:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-475.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px"
         id="tspan1594-5-6">LGPL 3.0</tspan><tspan
         id="tspan12253-3-1"
         sodipodi:role="line"
         x="472.19449"
         y="-415.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">https://www.gnu.org/licenses/lgpl-3.0.html</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-360.47995"
         id="tspan5031-6" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-302.47992"
         id="tspan5033-1"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Each Card Measures (63mm x 88mm)</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-247.14656"
         id="tspan5634-6-0" /></text>
  </g>
</svg>
