<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:version="1.1-dev (60faa23, 2020-06-06)"
   sodipodi:docname="CLUB-8.svg"
   width="69.087997mm"
   height="93.979996mm"
   viewBox="0 0 261.11999 355.19999"
   version="1.1"
   id="svg8">
  <sodipodi:namedview
     inkscape:current-layer="svg8"
     inkscape:window-maximized="0"
     inkscape:window-y="0"
     inkscape:window-x="0"
     inkscape:cy="213.46377"
     inkscape:cx="7.9487512"
     inkscape:zoom="0.28299879"
     showgrid="false"
     id="namedview21"
     inkscape:window-height="485"
     inkscape:window-width="774"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0"
     guidetolerance="10"
     gridtolerance="10"
     objecttolerance="10"
     borderopacity="1"
     bordercolor="#666666"
     pagecolor="#ffffff" />
  <title
     id="title8745">Vector Playing Cards</title>
  <defs
     id="defs2" />
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Vector Playing Cards</dc:title>
        <dc:creator>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:rights>
          <cc:Agent>
            <dc:title>Copyright 2011, 017</dc:title>
          </cc:Agent>
        </dc:rights>
        <dc:publisher>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:description>Vector Playing Cards</dc:description>
        <dc:contributor>
          <cc:Agent>
            <dc:title>Chris Aguilar</dc:title>
          </cc:Agent>
        </dc:contributor>
        <cc:license
           rdf:resource="https://opensource.org/licenses/lgpl-3.0.html" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1"
     transform="matrix(1.0966326,0,0,1.067954,-658.16655,-3.0399778)">
    <g
       transform="matrix(1.07145,0,0,1.07009,-1878.21,1167.5)"
       id="CLUB-8">
      <rect
         y="-1088.3696"
         x="2313.1089"
         height="310.81366"
         width="222.23224"
         id="rect6355-6-85-3-3-4-3"
         style="display:inline;opacity:1;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:0.996734;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         rx="10.62995"
         ry="10.629899" />
      <path
         d="m 2475.7563,-1055.3647 c -9.7745,0.2122 -14.1574,11.2172 -6.9184,19.7633 1.0499,1.2465 1.1582,1.9618 -0.7646,0.7645 -4.8738,-3.2592 -14.2192,-1.1166 -14.6978,8.8473 -0.5911,12.2769 16.5629,15.8142 19.7838,2.7596 0.17,-1.3773 1.2895,-1.3459 1.0195,0.6092 -0.2937,5.3064 -1.9509,10.266 -4.2287,15.1025 3.8391,0 8.1296,0 11.6203,0 -2.2777,-4.8365 -3.935,-9.7961 -4.2287,-15.1025 -0.2699,-1.9551 0.8495,-1.9865 1.0195,-0.6092 3.2209,13.0546 20.3749,9.5173 19.7838,-2.7596 -0.4785,-9.9639 -9.824,-12.1065 -14.6979,-8.8473 -1.9227,1.1973 -1.8144,0.482 -0.7645,-0.7645 7.239,-8.5461 2.8561,-19.5511 -6.9183,-19.7633 0,-10e-5 0,0 -0.01,0 z"
         id="path147-0-8-1-6-13-2-2-3-8"
         style="display:inline;opacity:1" />
      <path
         d="m 2372.6855,-1055.3647 c -9.7745,0.2122 -14.1574,11.2172 -6.9184,19.7633 1.0499,1.2465 1.1582,1.9618 -0.7646,0.7645 -4.8738,-3.2592 -14.2191,-1.1166 -14.6978,8.8473 -0.591,12.2769 16.5629,15.8142 19.7837,2.7596 0.1701,-1.3773 1.2895,-1.3459 1.0196,0.6092 -0.2937,5.3064 -1.9509,10.266 -4.2287,15.1025 3.8391,0 8.1296,0 11.6202,0 -2.2776,-4.8365 -3.9349,-9.7961 -4.2286,-15.1025 -0.2699,-1.9551 0.8495,-1.9865 1.0195,-0.6092 3.2209,13.0546 20.3749,9.5173 19.7838,-2.7596 -0.4785,-9.9639 -9.824,-12.1065 -14.6979,-8.8473 -1.9228,1.1973 -1.8144,0.482 -0.7645,-0.7645 7.239,-8.5461 2.8561,-19.5511 -6.9183,-19.7633 0,-10e-5 0,0 -0.01,0 z"
         id="path147-0-8-1-6-75-2-83-9-8-9"
         style="display:inline;opacity:1" />
      <path
         d="m 2475.7563,-809.73169 c -9.7745,-0.21225 -14.1574,-11.21726 -6.9184,-19.76328 1.0499,-1.24653 1.1582,-1.96187 -0.7646,-0.76452 -4.8738,3.25919 -14.2191,1.11662 -14.6978,-8.84728 -0.591,-12.27695 16.5629,-15.81427 19.7838,-2.75967 0.17,1.37736 1.2895,1.34593 1.0195,-0.60916 -0.2937,-5.30642 -1.9509,-10.26606 -4.2287,-15.10253 3.8391,0 8.1296,0 11.6203,0 -2.2777,4.83647 -3.935,9.79611 -4.2287,15.10253 -0.2699,1.95509 0.8495,1.98652 1.0195,0.60916 3.2209,-13.0546 20.3749,-9.51728 19.7838,2.75967 -0.4785,9.9639 -9.824,12.10647 -14.6979,8.84728 -1.9227,-1.19735 -1.8144,-0.48201 -0.7645,0.76452 7.239,8.54602 2.8561,19.55102 -6.9183,19.76328 0,6e-5 0,-5e-5 -0.01,0 z"
         id="path147-0-8-1-6-7-5-5-7-62-3-0"
         style="display:inline;opacity:1" />
      <path
         d="m 2372.6855,-809.73169 c -9.7745,-0.21225 -14.1574,-11.21726 -6.9184,-19.76328 1.0499,-1.24653 1.1582,-1.96187 -0.7646,-0.76452 -4.8738,3.25919 -14.2191,1.11662 -14.6978,-8.84728 -0.591,-12.27695 16.5629,-15.81427 19.7837,-2.75967 0.1701,1.37736 1.2895,1.34593 1.0196,-0.60916 -0.2937,-5.30642 -1.9509,-10.26606 -4.2287,-15.10253 3.8391,0 8.1296,0 11.6202,0 -2.2776,4.83647 -3.9349,9.79611 -4.2286,15.10253 -0.2699,1.95509 0.8495,1.98652 1.0195,0.60916 3.2209,-13.0546 20.3749,-9.51728 19.7838,2.75967 -0.4785,9.9639 -9.824,12.10647 -14.6979,8.84728 -1.9228,-1.19735 -1.8144,-0.48201 -0.7645,0.76452 7.239,8.54602 2.8561,19.55102 -6.9183,19.76328 0,6e-5 0,-5e-5 -0.01,0 z"
         id="path147-0-8-1-6-75-6-1-2-41-4-5"
         style="display:inline;opacity:1" />
      <path
         d="m 2424.2211,-1005.9179 c -9.7745,0.2122 -14.1573,11.21724 -6.9184,19.76325 1.0499,1.24653 1.1583,1.96188 -0.7646,0.76453 -4.8737,-3.25919 -14.2191,-1.11663 -14.6977,8.84727 -0.5911,12.27694 16.5628,15.81426 19.7836,2.75966 0.1701,-1.37736 1.2895,-1.34593 1.0196,0.60917 -0.2937,5.30641 -1.9509,10.26605 -4.2287,15.10253 3.8391,0 8.1296,0 11.6202,0 -2.2777,-4.83648 -3.9349,-9.79612 -4.2286,-15.10253 -0.2699,-1.9551 0.8495,-1.98653 1.0195,-0.60917 3.2209,13.0546 20.3749,9.51728 19.7837,-2.75966 -0.4784,-9.9639 -9.8239,-12.10646 -14.6978,-8.84727 -1.9228,1.19735 -1.8144,0.482 -0.7645,-0.76453 7.239,-8.54601 2.856,-19.55105 -6.9183,-19.76325 0,-10e-5 0,0 -0.01,0 z"
         id="path147-0-8-1-6-2-00-7-5-5-3"
         style="display:inline;opacity:1" />
      <path
         d="m 2475.7563,-956.47143 c -9.7745,0.21226 -14.1574,11.21727 -6.9184,19.76328 1.0499,1.24653 1.1582,1.96188 -0.7646,0.76453 -4.8738,-3.25919 -14.2192,-1.11663 -14.6978,8.84728 -0.591,12.27694 16.5629,15.81426 19.7838,2.75966 0.17,-1.37736 1.2894,-1.34593 1.0195,0.60917 -0.2937,5.30641 -1.9509,10.26605 -4.2287,15.10253 3.8391,0 8.1296,0 11.6203,0 -2.2777,-4.83648 -3.935,-9.79612 -4.2287,-15.10253 -0.2699,-1.9551 0.8495,-1.98653 1.0195,-0.60917 3.2209,13.0546 20.3749,9.51728 19.7838,-2.75966 -0.4785,-9.96391 -9.824,-12.10647 -14.6979,-8.84728 -1.9227,1.19735 -1.8144,0.482 -0.7645,-0.76453 7.239,-8.54601 2.8561,-19.55101 -6.9183,-19.76328 0,-6e-5 0,5e-5 -0.01,0 z"
         id="path147-0-8-1-6-7-5-7-3-0-4-4-6"
         style="display:inline;opacity:1" />
      <path
         d="m 2372.6855,-956.47143 c -9.7745,0.21226 -14.1574,11.21727 -6.9184,19.76328 1.0499,1.24653 1.1582,1.96188 -0.7646,0.76453 -4.8738,-3.25919 -14.2191,-1.11663 -14.6977,8.84728 -0.5911,12.27694 16.5628,15.81426 19.7836,2.75966 0.1701,-1.37736 1.2895,-1.34593 1.0196,0.60917 -0.2937,5.30641 -1.9509,10.26605 -4.2287,15.10253 3.8391,0 8.1296,0 11.6202,0 -2.2776,-4.83648 -3.9349,-9.79612 -4.2286,-15.10253 -0.2699,-1.9551 0.8495,-1.98653 1.0195,-0.60917 3.2209,13.0546 20.3749,9.51728 19.7838,-2.75966 -0.4785,-9.96391 -9.824,-12.10647 -14.6979,-8.84728 -1.9228,1.19735 -1.8144,0.482 -0.7645,-0.76453 7.239,-8.54601 2.8561,-19.55101 -6.9183,-19.76328 0,-6e-5 0,5e-5 -0.01,0 z"
         id="path147-0-8-1-6-75-6-6-5-3-2-4-8"
         style="display:inline;opacity:1" />
      <path
         d="m 2424.2211,-859.17835 c -9.7744,-0.21225 -14.1574,-11.21725 -6.9183,-19.76327 1.0499,-1.24653 1.1582,-1.96187 -0.7646,-0.76452 -4.8739,3.25919 -14.2193,1.11662 -14.6979,-8.84728 -0.5911,-12.27696 16.563,-15.81428 19.7837,-2.75967 0.1702,1.37736 1.2895,1.34593 1.0196,-0.60916 -0.2937,-5.30642 -1.9508,-10.26607 -4.2286,-15.10254 3.839,0 8.1296,0 11.6201,0 -2.2777,4.83647 -3.9349,9.79612 -4.2286,15.10254 -0.2698,1.95509 0.8495,1.98652 1.0195,0.60916 3.2209,-13.05461 20.3749,-9.51729 19.7837,2.75967 -0.4784,9.9639 -9.8239,12.10647 -14.6978,8.84728 -1.9228,-1.19735 -1.8144,-0.48201 -0.7645,0.76452 7.239,8.54602 2.8561,19.55101 -6.9182,19.76327 0,6e-5 0,-5e-5 -0.01,0 z"
         id="path147-0-8-1-6-2-0-8-6-42-9-6"
         style="display:inline;opacity:1" />
      <path
         d="m 2518.203,-827.36917 c 3.9097,-0.0884 5.663,-4.67386 2.7674,-8.23469 -0.4201,-0.5194 -0.4634,-0.81746 0.3058,-0.31856 1.9495,1.358 5.6877,0.46526 5.8791,-3.68637 0.2365,-5.1154 -6.6252,-6.58928 -7.9135,-1.14986 -0.068,0.57391 -0.5158,0.56081 -0.4078,-0.25382 0.1175,-2.211 0.7803,-4.27752 1.6914,-6.29272 -1.5492,0 -3.0986,0 -4.6478,0 0.9111,2.0152 1.5739,4.08172 1.6914,6.29272 0.108,0.81463 -0.3398,0.82773 -0.4078,0.25382 -1.2883,-5.43942 -8.1499,-3.96554 -7.9135,1.14986 0.1914,4.15163 3.9296,5.04437 5.8791,3.68637 0.7692,-0.4989 0.7259,-0.20084 0.3059,0.31856 -2.8956,3.56083 -1.1424,8.14624 2.7673,8.23469 0,2e-5 0,-2e-5 0,0 z"
         id="path147-6-9-8-7-0-5-6-1-6-9"
         style="display:inline;opacity:1" />
      <path
         d="m 2330.248,-1038.5561 c -3.9098,0.088 -5.663,4.6739 -2.7674,8.2347 0.42,0.5194 0.4632,0.8174 -0.3058,0.3185 -1.9497,-1.358 -5.6877,-0.4652 -5.8792,3.6864 -0.2364,5.1154 6.6252,6.5893 7.9135,1.1499 0.068,-0.5739 0.5159,-0.5608 0.4078,0.2538 -0.1175,2.211 -0.7803,4.2775 -1.6914,6.2927 1.5493,0 3.0986,0 4.6479,0 -0.9111,-2.0152 -1.574,-4.0817 -1.6915,-6.2927 -0.1079,-0.8146 0.3398,-0.8277 0.4078,-0.2538 1.2883,5.4394 8.15,3.9655 7.9135,-1.1499 -0.1914,-4.1516 -3.9295,-5.0444 -5.8791,-3.6864 -0.7691,0.4989 -0.7258,0.2009 -0.3059,-0.3185 2.8957,-3.5608 1.1424,-8.1462 -2.7672,-8.2347 0,0 0,0 0,0 z"
         id="path147-6-9-8-7-4-5-62-4-0-0"
         style="display:inline;opacity:1" />
      <path
         d="m 2319.5761,-1051.3169 c 0.1561,-4.4159 1.751,-6.0723 4.8907,-8.3828 -2.5049,-1.8091 -3.5608,-4.4759 -3.3458,-6.7458 0.5158,-5.4967 4.755,-8.2444 9.3808,-8.1269 4.6295,0.1227 8.614,3.7971 8.925,7.7914 0.2701,3.4721 -0.7603,5.1482 -3.2204,6.9743 2.9196,2.4684 4.7845,4.0896 4.7154,8.4389 -0.069,4.3493 -3.7644,9.5249 -10.2051,9.6828 -6.4406,0.1579 -11.3006,-5.216 -11.1406,-9.6319 z m 17.9795,0.016 c 0.2751,-3.9982 -3.255,-6.6949 -7.2395,-6.7341 -3.8795,-0.042 -7.3495,3.2737 -7.3495,6.8634 0,3.8546 4.5144,5.9496 7.4301,5.9587 3.5443,0.016 6.9194,-2.6459 7.1589,-6.088 z m -1.2749,-14.3023 c 0.076,-3.3638 -3.0246,-5.7773 -6.2244,-5.6807 -2.8453,0.087 -5.8545,2.2595 -5.8596,5.0737 0,3.5231 3.4252,5.2826 6.3499,5.3505 2.6546,0.06 5.6739,-2.1995 5.7341,-4.7435 z m 0,0"
         id="path27-3-3-2"
         style="opacity:1" />
      <path
         d="m 2507.5282,-814.60835 c 0.1561,4.41593 1.7509,6.07232 4.8907,8.3828 -2.5049,1.8091 -3.5609,4.47584 -3.3458,6.74586 0.5158,5.49666 4.755,8.24434 9.3807,8.12682 4.6296,-0.12271 8.614,-3.79712 8.925,-7.7914 0.2701,-3.47216 -0.7603,-5.14818 -3.2203,-6.97432 2.9196,-2.46828 4.7844,-4.08948 4.7153,-8.43882 -0.069,-4.34934 -3.7643,-9.52494 -10.205,-9.68283 -6.4407,-0.15799 -11.3007,5.21606 -11.1406,9.63189 z m 17.9794,-0.0159 c 0.2752,3.99817 -3.2549,6.69502 -7.2394,6.73419 -3.8796,0.0419 -7.3495,-3.27379 -7.3495,-6.86337 0,-3.85463 4.5144,-5.94961 7.4301,-5.95878 3.5442,-0.0159 6.9194,2.64591 7.1588,6.08796 z m -1.2749,14.30241 c 0.076,3.3637 -3.0245,5.77726 -6.2244,5.68067 -2.8452,-0.0877 -5.8544,-2.25945 -5.8595,-5.07372 0,-3.52309 3.4251,-5.28265 6.3498,-5.35053 2.6546,-0.0598 5.674,2.19944 5.7341,4.74358 z m 0,0"
         id="path27-0-0-5-0"
         style="opacity:1" />
    </g>
  </g>
  <g
     inkscape:transform-center-y="325.08974"
     inkscape:transform-center-x="155.4777"
     transform="translate(-329.77263,156.0626)"
     id="g862"
     sodipodi:insensitive="true">
    <rect
       ry="124.68219"
       rx="144.48164"
       y="-1029.6816"
       x="-356.47275"
       height="794.39795"
       width="1659.9126"
       id="rect22845-2"
       style="opacity:1;vector-effect:none;fill:#006614;fill-opacity:1;stroke:none;stroke-width:1.74801;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <text
       onclick=""
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:42.6667px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.06667px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       x="472.19449"
       y="-935.81342"
       id="text5630-0"><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-935.81342"
         id="tspan4923"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Single Playing Card from:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-875.81342"
         id="tspan5035"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Standard Edition - Color Set - Bordered </tspan><tspan
         id="tspan12311"
         sodipodi:role="line"
         x="472.19449"
         y="-820.48004" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-762.48004"
         id="tspan4927"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Copyright 2011, 2020 - Chris Aguilar - <EMAIL></tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-707.14667"
         id="tspan12344" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-649.14667"
         id="tspan4931"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">https://totalnonsense.com/open-source-vector-playing-cards/</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-593.81329"
         id="tspan4929" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-535.81329"
         id="tspan4919"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">Licensed under:</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-475.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px"
         id="tspan1594-5">LGPL 3.0</tspan><tspan
         id="tspan12253-3"
         sodipodi:role="line"
         x="472.19449"
         y="-415.81329"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffaaaa;fill-opacity:1;stroke-width:1.06667px">https://www.gnu.org/licenses/lgpl-3.0.html</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-360.47995"
         id="tspan5031" /><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-302.47992"
         id="tspan5033"
         style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:48px;line-height:125%;font-family:Carlito;-inkscape-font-specification:'Carlito, Normal';text-align:center;writing-mode:lr-tb;text-anchor:middle;fill:#ffffff;fill-opacity:1;stroke-width:1.06667px">Each Card Measures (63mm x 88mm)</tspan><tspan
         sodipodi:role="line"
         x="472.19449"
         y="-247.14656"
         id="tspan5634-6" /></text>
  </g>
</svg>
