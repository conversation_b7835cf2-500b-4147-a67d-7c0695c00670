const GameManager = require('../controllers/GameManager');

/**
 * 房间类
 * 管理游戏房间的创建、玩家加入/离开、房间状态等
 */
class Room {
    constructor(roomCode, adminId, adminName) {
        this.roomCode = roomCode;
        this.adminId = adminId;
        this.adminName = adminName;
        this.createdAt = new Date();
        this.lastActivity = new Date();
        
        // 房间状态
        this.status = 'waiting'; // waiting, playing, finished
        this.maxPlayers = 8;
        this.minPlayers = 2;
        
        // 玩家管理
        this.players = new Map(); // socketId -> playerInfo
        this.playerOrder = []; // 玩家加入顺序
        
        // 游戏管理器
        this.gameManager = new GameManager();
        
        // 房间设置
        this.settings = {
            autoStart: false,
            roundLimit: 0, // 0表示无限制
            timeLimit: 0   // 0表示无限制（分钟）
        };
        
        // 统计信息
        this.stats = {
            totalRounds: 0,
            totalPlayers: 0,
            createdAt: this.createdAt
        };
    }
    
    /**
     * 生成房间码
     * @returns {string} 6位数字房间码
     */
    static generateRoomCode() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
    
    /**
     * 验证房间码格式
     * @param {string} roomCode 房间码
     * @returns {boolean} 是否有效
     */
    static isValidRoomCode(roomCode) {
        return /^\d{6}$/.test(roomCode);
    }
    
    /**
     * 添加玩家到房间
     * @param {string} socketId Socket连接ID
     * @param {string} playerName 玩家名称
     * @returns {Object} 玩家信息
     */
    addPlayer(socketId, playerName) {
        // 检查房间是否已满
        if (this.players.size >= this.maxPlayers) {
            throw new Error('房间已满');
        }
        
        // 检查游戏是否正在进行
        if (this.status === 'playing') {
            throw new Error('游戏正在进行中，无法加入');
        }
        
        // 检查玩家名是否重复
        for (const player of this.players.values()) {
            if (player.name === playerName) {
                throw new Error('玩家名已存在');
            }
        }
        
        // 创建玩家信息
        const playerInfo = {
            socketId: socketId,
            name: playerName,
            joinedAt: new Date(),
            isAdmin: socketId === this.adminId,
            isReady: false,
            stats: {
                gamesPlayed: 0,
                gamesWon: 0,
                winRate: 0
            }
        };
        
        // 添加到房间
        this.players.set(socketId, playerInfo);
        this.playerOrder.push(socketId);
        
        // 添加到游戏管理器
        this.gameManager.addPlayer(socketId, playerName);
        
        // 更新统计
        this.stats.totalPlayers = Math.max(this.stats.totalPlayers, this.players.size);
        this.updateActivity();
        
        return playerInfo;
    }
    
    /**
     * 移除玩家
     * @param {string} socketId Socket连接ID
     * @returns {boolean} 是否成功移除
     */
    removePlayer(socketId) {
        const player = this.players.get(socketId);
        if (!player) {
            return false;
        }
        
        // 从房间移除
        this.players.delete(socketId);
        this.playerOrder = this.playerOrder.filter(id => id !== socketId);
        
        // 从游戏管理器移除
        try {
            this.gameManager.removePlayer(socketId);
        } catch (error) {
            console.warn('从游戏管理器移除玩家失败:', error.message);
        }
        
        // 如果是管理员离开且还有其他玩家，转移管理员权限
        if (socketId === this.adminId && this.players.size > 0) {
            this.transferAdmin();
        }
        
        this.updateActivity();
        return true;
    }
    
    /**
     * 转移管理员权限
     */
    transferAdmin() {
        if (this.playerOrder.length > 0) {
            const newAdminId = this.playerOrder[0];
            const newAdmin = this.players.get(newAdminId);
            
            if (newAdmin) {
                this.adminId = newAdminId;
                this.adminName = newAdmin.name;
                newAdmin.isAdmin = true;
                
                console.log(`管理员权限转移给: ${newAdmin.name}`);
            }
        }
    }
    
    /**
     * 获取玩家信息
     * @param {string} socketId Socket连接ID
     * @returns {Object|null} 玩家信息
     */
    getPlayer(socketId) {
        return this.players.get(socketId) || null;
    }
    
    /**
     * 获取所有玩家列表
     * @returns {Array} 玩家列表
     */
    getAllPlayers() {
        return Array.from(this.players.values()).map(player => ({
            socketId: player.socketId,
            name: player.name,
            isAdmin: player.isAdmin,
            isReady: player.isReady,
            joinedAt: player.joinedAt,
            stats: player.stats
        }));
    }
    
    /**
     * 设置玩家准备状态
     * @param {string} socketId Socket连接ID
     * @param {boolean} ready 是否准备
     */
    setPlayerReady(socketId, ready) {
        const player = this.players.get(socketId);
        if (player) {
            player.isReady = ready;
            this.updateActivity();
        }
    }
    
    /**
     * 检查是否所有玩家都准备好了
     * @returns {boolean} 是否都准备好
     */
    areAllPlayersReady() {
        if (this.players.size < this.minPlayers) {
            return false;
        }
        
        for (const player of this.players.values()) {
            if (!player.isReady) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 开始游戏
     * @returns {Object} 游戏开始信息
     */
    startGame() {
        if (this.status === 'playing') {
            throw new Error('游戏已经在进行中');
        }
        
        if (this.players.size < this.minPlayers) {
            throw new Error(`至少需要${this.minPlayers}个玩家才能开始游戏`);
        }
        
        // 重置所有玩家的准备状态
        for (const player of this.players.values()) {
            player.isReady = false;
        }
        
        this.status = 'playing';
        this.stats.totalRounds++;
        
        // 启动游戏管理器
        const gameInfo = this.gameManager.startNewGame();
        
        this.updateActivity();
        
        return {
            roomCode: this.roomCode,
            status: this.status,
            round: this.stats.totalRounds,
            ...gameInfo
        };
    }
    
    /**
     * 结束游戏
     * @returns {Object} 游戏结束信息
     */
    endGame() {
        this.status = 'waiting';
        
        // 更新玩家统计
        this.updatePlayerStats();
        
        this.updateActivity();
        
        return {
            roomCode: this.roomCode,
            status: this.status,
            totalRounds: this.stats.totalRounds
        };
    }
    
    /**
     * 更新玩家统计信息
     */
    updatePlayerStats() {
        const gameHistory = this.gameManager.getGameHistory();
        if (gameHistory.length === 0) return;
        
        const latestGame = gameHistory[gameHistory.length - 1];
        
        // 更新所有玩家的游戏次数
        for (const player of this.players.values()) {
            player.stats.gamesPlayed++;
        }
        
        // 更新获胜者的胜利次数
        for (const player of this.players.values()) {
            const gameManagerPlayer = this.gameManager.getPlayerInfo(player.socketId);
            player.stats.gamesWon = gameManagerPlayer.stats.totalWins;
            player.stats.winRate = parseFloat(gameManagerPlayer.stats.winRate);
        }
    }
    
    /**
     * 获取房间状态
     * @returns {Object} 房间状态信息
     */
    getRoomStatus() {
        return {
            roomCode: this.roomCode,
            status: this.status,
            adminId: this.adminId,
            adminName: this.adminName,
            playerCount: this.players.size,
            maxPlayers: this.maxPlayers,
            minPlayers: this.minPlayers,
            allReady: this.areAllPlayersReady(),
            canStart: this.canStartGame(),
            createdAt: this.createdAt,
            lastActivity: this.lastActivity,
            settings: this.settings,
            stats: this.stats
        };
    }
    
    /**
     * 检查是否可以开始游戏
     * @returns {boolean} 是否可以开始
     */
    canStartGame() {
        return this.status === 'waiting' && 
               this.players.size >= this.minPlayers && 
               this.players.size <= this.maxPlayers;
    }
    
    /**
     * 更新房间活动时间
     */
    updateActivity() {
        this.lastActivity = new Date();
    }
    
    /**
     * 检查房间是否为空
     * @returns {boolean} 是否为空
     */
    isEmpty() {
        return this.players.size === 0;
    }
    
    /**
     * 检查房间是否过期（超过指定时间无活动）
     * @param {number} timeoutMinutes 超时时间（分钟）
     * @returns {boolean} 是否过期
     */
    isExpired(timeoutMinutes = 60) {
        const now = new Date();
        const diffMinutes = (now - this.lastActivity) / (1000 * 60);
        return diffMinutes > timeoutMinutes;
    }
    
    /**
     * 获取房间详细信息
     * @returns {Object} 详细信息
     */
    getDetailedInfo() {
        return {
            ...this.getRoomStatus(),
            players: this.getAllPlayers(),
            gameState: this.gameManager.getGameState(),
            gameHistory: this.gameManager.getGameHistory()
        };
    }
    
    /**
     * 更新房间设置
     * @param {Object} newSettings 新设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.updateActivity();
    }
}

module.exports = Room;
