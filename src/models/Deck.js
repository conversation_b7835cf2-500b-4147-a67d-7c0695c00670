const Card = require('./Card');

/**
 * 牌库类
 * 管理一副52张扑克牌的生成、洗牌、发牌等操作
 */
class Deck {
    constructor() {
        this.cards = [];
        this.dealtCards = [];
        this.initialize();
    }
    
    /**
     * 初始化一副完整的52张牌
     */
    initialize() {
        this.cards = [];
        this.dealtCards = [];
        
        const suits = Card.getAllSuits();
        const ranks = Card.getAllRanks();
        
        // 生成52张牌
        for (const suit of suits) {
            for (const rank of ranks) {
                this.cards.push(new Card(suit, rank));
            }
        }
    }
    
    /**
     * 洗牌 - 使用Fisher-Yates洗牌算法
     */
    shuffle() {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }
        return this;
    }
    
    /**
     * 发一张牌
     * @returns {Card|null} 返回发出的牌，如果没有牌则返回null
     */
    dealCard() {
        if (this.cards.length === 0) {
            console.warn('牌库已空，无法发牌');
            return null;
        }
        
        const card = this.cards.pop();
        this.dealtCards.push(card);
        return card;
    }
    
    /**
     * 发多张牌
     * @param {number} count 要发的牌数
     * @returns {Card[]} 发出的牌数组
     */
    dealCards(count) {
        const cards = [];
        for (let i = 0; i < count; i++) {
            const card = this.dealCard();
            if (card) {
                cards.push(card);
            } else {
                console.warn(`只能发出${cards.length}张牌，牌库不足`);
                break;
            }
        }
        return cards;
    }
    
    /**
     * 为多个玩家发牌
     * @param {number} playerCount 玩家数量
     * @param {number} cardsPerPlayer 每个玩家的牌数
     * @returns {Card[][]} 每个玩家的手牌数组
     */
    dealToPlayers(playerCount, cardsPerPlayer = 5) {
        const totalCardsNeeded = playerCount * cardsPerPlayer;
        
        if (this.cards.length < totalCardsNeeded) {
            throw new Error(`牌库不足：需要${totalCardsNeeded}张牌，但只有${this.cards.length}张`);
        }
        
        const playerHands = [];
        
        // 轮流给每个玩家发牌
        for (let i = 0; i < playerCount; i++) {
            playerHands.push([]);
        }
        
        for (let cardIndex = 0; cardIndex < cardsPerPlayer; cardIndex++) {
            for (let playerIndex = 0; playerIndex < playerCount; playerIndex++) {
                const card = this.dealCard();
                if (card) {
                    playerHands[playerIndex].push(card);
                }
            }
        }
        
        return playerHands;
    }
    
    /**
     * 重置牌库（收回所有牌并重新洗牌）
     */
    reset() {
        this.initialize();
        this.shuffle();
        return this;
    }
    
    /**
     * 获取剩余牌数
     */
    getRemainingCount() {
        return this.cards.length;
    }
    
    /**
     * 获取已发牌数
     */
    getDealtCount() {
        return this.dealtCards.length;
    }
    
    /**
     * 检查牌库是否为空
     */
    isEmpty() {
        return this.cards.length === 0;
    }
    
    /**
     * 检查是否有足够的牌
     */
    hasEnoughCards(count) {
        return this.cards.length >= count;
    }
    
    /**
     * 获取牌库状态信息
     */
    getStatus() {
        return {
            remaining: this.getRemainingCount(),
            dealt: this.getDealtCount(),
            total: 52,
            isEmpty: this.isEmpty()
        };
    }
    
    /**
     * 查看顶部的几张牌（不发牌）
     */
    peek(count = 1) {
        if (count > this.cards.length) {
            count = this.cards.length;
        }
        return this.cards.slice(-count);
    }
    
    /**
     * 转换为JSON对象
     */
    toJSON() {
        return {
            cards: this.cards.map(card => card.toJSON()),
            dealtCards: this.dealtCards.map(card => card.toJSON()),
            status: this.getStatus()
        };
    }
    
    /**
     * 从JSON对象恢复Deck实例
     */
    static fromJSON(json) {
        const deck = new Deck();
        deck.cards = json.cards.map(cardData => Card.fromJSON(cardData));
        deck.dealtCards = json.dealtCards.map(cardData => Card.fromJSON(cardData));
        return deck;
    }
}

module.exports = Deck;
