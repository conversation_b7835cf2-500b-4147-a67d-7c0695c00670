/**
 * 扑克牌类
 * 表示一张扑克牌，包含花色、点数和显示信息
 */
class Card {
    constructor(suit, rank) {
        this.suit = suit;   // 花色：'hearts', 'diamonds', 'clubs', 'spades'
        this.rank = rank;   // 点数：1-13 (1=A, 11=J, 12=Q, 13=K)
        this.value = this.calculateValue();
        this.display = this.getDisplayString();
        this.color = this.getColor();
    }
    
    /**
     * 计算牌的数值（用于牛牛计算）
     * A=1, 2-10=面值, J/Q/K=10
     */
    calculateValue() {
        if (this.rank === 1) return 1;  // A = 1
        if (this.rank >= 11) return 10; // J/Q/K = 10
        return this.rank;               // 2-10 = 面值
    }
    
    /**
     * 获取牌的显示字符串
     */
    getDisplayString() {
        const suitSymbols = {
            'hearts': '♥',
            'diamonds': '♦',
            'clubs': '♣',
            'spades': '♠'
        };
        
        const rankNames = {
            1: 'A',
            11: 'J',
            12: 'Q',
            13: 'K'
        };
        
        const rankDisplay = rankNames[this.rank] || this.rank.toString();
        const suitSymbol = suitSymbols[this.suit];
        
        return `${rankDisplay}${suitSymbol}`;
    }
    
    /**
     * 获取牌的颜色（红色或黑色）
     */
    getColor() {
        return (this.suit === 'hearts' || this.suit === 'diamonds') ? 'red' : 'black';
    }
    
    /**
     * 获取牌的完整信息
     */
    getInfo() {
        return {
            suit: this.suit,
            rank: this.rank,
            value: this.value,
            display: this.display,
            color: this.color
        };
    }
    
    /**
     * 比较两张牌的大小
     * 返回: 1(当前牌大), -1(当前牌小), 0(相等)
     */
    compareTo(otherCard) {
        if (this.rank > otherCard.rank) return 1;
        if (this.rank < otherCard.rank) return -1;
        return 0;
    }
    
    /**
     * 检查是否为同一张牌
     */
    equals(otherCard) {
        return this.suit === otherCard.suit && this.rank === otherCard.rank;
    }
    
    /**
     * 转换为JSON对象
     */
    toJSON() {
        return {
            suit: this.suit,
            rank: this.rank,
            value: this.value,
            display: this.display,
            color: this.color
        };
    }
    
    /**
     * 从JSON对象创建Card实例
     */
    static fromJSON(json) {
        return new Card(json.suit, json.rank);
    }
    
    /**
     * 获取所有花色
     */
    static getAllSuits() {
        return ['hearts', 'diamonds', 'clubs', 'spades'];
    }
    
    /**
     * 获取所有点数
     */
    static getAllRanks() {
        return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
    }
    
    /**
     * 获取花色的中文名称
     */
    static getSuitName(suit) {
        const names = {
            'hearts': '红桃',
            'diamonds': '方块',
            'clubs': '梅花',
            'spades': '黑桃'
        };
        return names[suit] || suit;
    }
    
    /**
     * 获取点数的中文名称
     */
    static getRankName(rank) {
        const names = {
            1: 'A',
            11: 'J',
            12: 'Q',
            13: 'K'
        };
        return names[rank] || rank.toString();
    }
}

module.exports = Card;
