/**
 * 牛牛游戏计算工具类
 * 处理牛牛游戏的核心逻辑：牌型判断、点数计算、比牌等
 */
class NiuniuCalculator {
    
    /**
     * 计算手牌的牛牛结果
     * @param {Card[]} cards 5张手牌
     * @returns {Object} 牛牛结果对象
     */
    static calculateNiuniu(cards) {
        if (!cards || cards.length !== 5) {
            throw new Error('牛牛游戏需要5张牌');
        }
        
        // 获取所有牌的数值
        const values = cards.map(card => card.value);
        
        // 检查特殊牌型
        const specialType = this.checkSpecialTypes(cards, values);
        if (specialType) {
            return specialType;
        }
        
        // 计算普通牛牛
        return this.calculateRegularNiuniu(cards, values);
    }
    
    /**
     * 检查特殊牌型
     */
    static checkSpecialTypes(cards, values) {
        // 五小牛：5张牌都小于5且总和小于等于10
        if (this.isFiveSmallNiu(values)) {
            return {
                type: '五小牛',
                points: 50,  // 最高点数
                multiplier: 5,
                cards: cards.map(card => card.toJSON()),
                description: '5张牌都小于5且总和≤10'
            };
        }
        
        // 炸弹：4张相同点数的牌
        if (this.isBomb(values)) {
            return {
                type: '炸弹',
                points: 40,
                multiplier: 4,
                cards: cards.map(card => card.toJSON()),
                description: '4张相同点数的牌'
            };
        }
        
        // 五花牛：5张牌都是J、Q、K
        if (this.isFiveFlowerNiu(cards)) {
            return {
                type: '五花牛',
                points: 30,
                multiplier: 3,
                cards: cards.map(card => card.toJSON()),
                description: '5张牌都是J、Q、K'
            };
        }
        
        return null;
    }
    
    /**
     * 计算普通牛牛
     */
    static calculateRegularNiuniu(cards, values) {
        // 尝试找到前3张牌组成10的倍数的组合
        const combinations = this.getThreeCardCombinationsWithIndices(values);
        
        let bestResult = null;
        
        for (const combo of combinations) {
            const sum = combo.values.reduce((a, b) => a + b, 0);
            if (sum % 10 === 0) {
                // 找到了"有牛"的组合
                const remainingIndices = values.map((_, index) => index)
                    .filter(index => !combo.indices.includes(index));
                const remainingValues = remainingIndices.map(index => values[index]);
                const backTwoSum = remainingValues.reduce((a, b) => a + b, 0);
                const points = backTwoSum % 10;
                
                let result;
                if (points === 0) {
                    // 牛牛
                    result = {
                        type: '牛牛',
                        points: 20,
                        multiplier: 2,
                        cards: cards.map(card => card.toJSON()),
                        description: '前3张组成10的倍数，后2张也是10的倍数'
                    };
                } else {
                    // 有牛
                    result = {
                        type: `牛${points}`,
                        points: points,
                        multiplier: 1,
                        cards: cards.map(card => card.toJSON()),
                        description: `前3张组成10的倍数，后2张点数为${points}`
                    };
                }
                
                // 更新最佳结果
                if (!bestResult || result.points > bestResult.points) {
                    bestResult = result;
                }
            }
        }
        
        // 如果找到了牛
        if (bestResult) {
            return bestResult;
        }
        
        // 没有牛
        return {
            type: '没牛',
            points: 0,
            multiplier: 1,
            cards: cards.map(card => card.toJSON()),
            description: '无法组成10的倍数'
        };
    }
    
    /**
     * 获取5张牌中3张牌的所有组合（包含值和索引）
     */
    static getThreeCardCombinationsWithIndices(values) {
        const combinations = [];
        const n = values.length;
        
        for (let i = 0; i < n - 2; i++) {
            for (let j = i + 1; j < n - 1; j++) {
                for (let k = j + 1; k < n; k++) {
                    combinations.push({
                        values: [values[i], values[j], values[k]],
                        indices: [i, j, k]
                    });
                }
            }
        }
        
        return combinations;
    }
    
    /**
     * 检查是否为五小牛
     */
    static isFiveSmallNiu(values) {
        const sum = values.reduce((a, b) => a + b, 0);
        return values.every(val => val < 5) && sum <= 10;
    }
    
    /**
     * 检查是否为炸弹
     */
    static isBomb(values) {
        const counts = {};
        values.forEach(val => {
            counts[val] = (counts[val] || 0) + 1;
        });
        
        return Object.values(counts).some(count => count >= 4);
    }
    
    /**
     * 检查是否为五花牛
     */
    static isFiveFlowerNiu(cards) {
        return cards.every(card => card.rank >= 11 && card.rank <= 13);
    }
    
    /**
     * 比较两个牛牛结果的大小
     * @param {Object} result1 第一个结果
     * @param {Object} result2 第二个结果
     * @returns {number} 1(result1大), -1(result1小), 0(相等)
     */
    static compareResults(result1, result2) {
        // 先比较点数
        if (result1.points > result2.points) return 1;
        if (result1.points < result2.points) return -1;
        
        // 点数相同，比较最大牌
        const maxCard1 = this.getMaxCard(result1.cards);
        const maxCard2 = this.getMaxCard(result2.cards);
        
        if (maxCard1.rank > maxCard2.rank) return 1;
        if (maxCard1.rank < maxCard2.rank) return -1;
        
        // 最大牌也相同，比较花色（黑桃>红桃>梅花>方块）
        const suitOrder = { 'spades': 4, 'hearts': 3, 'clubs': 2, 'diamonds': 1 };
        const suit1 = suitOrder[maxCard1.suit];
        const suit2 = suitOrder[maxCard2.suit];
        
        if (suit1 > suit2) return 1;
        if (suit1 < suit2) return -1;
        
        return 0; // 完全相同
    }
    
    /**
     * 获取手牌中的最大牌
     */
    static getMaxCard(cards) {
        return cards.reduce((max, card) => {
            if (card.rank > max.rank) return card;
            if (card.rank === max.rank) {
                const suitOrder = { 'spades': 4, 'hearts': 3, 'clubs': 2, 'diamonds': 1 };
                if (suitOrder[card.suit] > suitOrder[max.suit]) return card;
            }
            return max;
        });
    }
    
    /**
     * 对多个玩家的结果进行排序
     * @param {Array} results 玩家结果数组
     * @returns {Array} 排序后的结果数组（从大到小）
     */
    static sortResults(results) {
        return results.sort((a, b) => this.compareResults(b, a));
    }
    
    /**
     * 获取牌型的倍数
     */
    static getMultiplier(type) {
        const multipliers = {
            '五小牛': 5,
            '炸弹': 4,
            '五花牛': 3,
            '牛牛': 2,
            '没牛': 1
        };
        
        // 牛1-牛9都是1倍
        if (type.startsWith('牛') && type !== '牛牛') {
            return 1;
        }
        
        return multipliers[type] || 1;
    }
    
    /**
     * 验证手牌是否有效
     */
    static validateHand(cards) {
        if (!cards || cards.length !== 5) {
            return { valid: false, error: '手牌必须是5张牌' };
        }
        
        // 检查是否有重复的牌
        const cardStrings = cards.map(card => `${card.suit}-${card.rank}`);
        const uniqueCards = new Set(cardStrings);
        
        if (uniqueCards.size !== cards.length) {
            return { valid: false, error: '手牌中有重复的牌' };
        }
        
        return { valid: true };
    }
}

module.exports = NiuniuCalculator;
