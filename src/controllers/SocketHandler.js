const RoomManager = require('./RoomManager');

/**
 * WebSocket事件处理器
 * 处理客户端的Socket.io连接和消息
 */
class SocketHandler {
    constructor(io) {
        this.io = io;
        this.roomManager = new RoomManager();
        this.setupSocketHandlers();
    }

    /**
     * 设置Socket事件处理器
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`用户连接: ${socket.id}`);

            // 检查是否有重新连接信息
            socket.on('reconnectRoom', (data) => {
                try {
                    const { oldSocketId, playerName, roomCode, isAdmin } = data;
                    const userType = isAdmin ? '管理员' : '玩家';
                    console.log(`收到${userType}重连请求: ${playerName} (${oldSocketId} -> ${socket.id}) 房间: ${roomCode}`);

                    if (!oldSocketId || !playerName || !roomCode) {
                        throw new Error('重连信息不完整');
                    }

                    // 验证房间和玩家信息
                    const room = this.roomManager.getRoom(roomCode);
                    if (!room) {
                        throw new Error('房间不存在或已被删除');
                    }

                    const player = room.getPlayer(oldSocketId);
                    if (!player) {
                        throw new Error('未找到玩家信息');
                    }

                    if (player.name !== playerName) {
                        throw new Error('玩家名称不匹配');
                    }

                    // 如果是管理员重连，验证管理员身份
                    if (isAdmin && room.adminId !== oldSocketId) {
                        throw new Error('管理员身份验证失败');
                    }

                    // 处理重新连接
                    const result = this.roomManager.handlePlayerReconnect(
                        oldSocketId, socket.id, playerName
                    );

                    if (result.success) {
                        // 加入Socket房间
                        socket.join(roomCode);

                        // 通知玩家重新连接成功
                        socket.emit('reconnectSuccess', {
                            success: true,
                            playerName: playerName,
                            ...result
                        });

                        // 广播玩家列表更新
                        this.broadcastToRoom(roomCode, 'playersUpdate', result.players);

                        const userType = isAdmin ? '管理员' : '玩家';
                        console.log(`${userType}重新连接成功: ${playerName} (${oldSocketId} -> ${socket.id}) -> ${roomCode}`);
                        return;
                    } else {
                        throw new Error(result.message || '重新连接处理失败');
                    }

                } catch (error) {
                    console.log(`玩家重新连接失败: ${error.message}`);
                    socket.emit('reconnectFailed', {
                        success: false,
                        message: error.message
                    });
                }
            });

            // 房间管理事件
            socket.on('createRoom', (data) => this.handleCreateRoom(socket, data));
            socket.on('joinGame', (data) => this.handleJoinGame(socket, data));
            socket.on('leaveRoom', () => this.handleLeaveRoom(socket));

            // 游戏控制事件（管理员）
            socket.on('startGame', () => this.handleStartGame(socket));
            socket.on('dealCards', () => this.handleDealCards(socket));
            socket.on('endGame', () => this.handleEndGame(socket));
            socket.on('removePlayer', (data) => this.handleRemovePlayer(socket, data));

            // 玩家状态事件
            socket.on('setReady', (data) => this.handleSetReady(socket, data));
            socket.on('getGameState', () => this.handleGetGameState(socket));
            socket.on('getPlayerHand', () => this.handleGetPlayerHand(socket));

            // 断线处理
            socket.on('disconnect', () => this.handleDisconnect(socket));

            // 发送连接成功消息
            socket.emit('connected', {
                socketId: socket.id,
                message: '连接成功'
            });
        });
    }

    /**
     * 处理创建房间
     */
    handleCreateRoom(socket, data) {
        try {
            const adminName = data?.adminName || '管理员';
            const result = this.roomManager.createRoom(socket.id, adminName);

            // 加入Socket房间（用于广播）
            socket.join(result.roomCode);

            socket.emit('roomCreated', {
                success: true,
                ...result
            });

            console.log(`房间创建: ${result.roomCode} by ${adminName}`);

        } catch (error) {
            socket.emit('roomError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理加入游戏
     */
    handleJoinGame(socket, data) {
        try {
            const { playerName, roomCode } = data;

            if (!playerName || !roomCode) {
                throw new Error('玩家名和房间码不能为空');
            }

            const result = this.roomManager.joinRoom(roomCode, socket.id, playerName);

            // 加入Socket房间
            socket.join(roomCode);

            // 通知玩家加入成功
            socket.emit('joinSuccess', {
                success: true,
                ...result
            });

            // 广播玩家列表更新
            this.broadcastToRoom(roomCode, 'playersUpdate', result.players);

            console.log(`玩家加入: ${playerName} -> ${roomCode}`);

        } catch (error) {
            socket.emit('joinError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理离开房间
     */
    handleLeaveRoom(socket) {
        try {
            const result = this.roomManager.leaveRoom(socket.id);

            if (result.success) {
                const roomCode = result.roomCode;

                // 离开Socket房间
                socket.leave(roomCode);

                socket.emit('leaveSuccess', result);

                // 如果房间还存在，广播玩家列表更新
                if (!result.roomDeleted && result.players) {
                    this.broadcastToRoom(roomCode, 'playersUpdate', result.players);
                }

                console.log(`玩家离开房间: ${socket.id}`);
            }

        } catch (error) {
            socket.emit('leaveError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理开始游戏（管理员）
     */
    handleStartGame(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            if (room.adminId !== socket.id) {
                throw new Error('只有管理员可以开始游戏');
            }

            const gameInfo = room.startGame();

            // 广播游戏开始
            this.broadcastToRoom(room.roomCode, 'gameStart', gameInfo);

            console.log(`游戏开始: ${room.roomCode}`);

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理发牌（管理员）
     */
    handleDealCards(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            if (room.adminId !== socket.id) {
                throw new Error('只有管理员可以发牌');
            }

            const hands = room.gameManager.dealCards();

            // 计算所有玩家的结果
            const results = room.gameManager.calculateResults();

            // 给每个玩家发送他们的手牌
            for (const [playerId, playerData] of Object.entries(hands)) {
                const playerSocket = this.io.sockets && this.io.sockets.sockets ?
                    this.io.sockets.sockets.get(playerId) : null;

                if (playerSocket) {
                    const playerResult = results.find(r => r.playerId === playerId);

                    playerSocket.emit('cardsDealt', {
                        cards: playerData.cards,
                        result: playerResult
                    });
                }
            }

            // 广播发牌完成和游戏结果
            this.broadcastToRoom(room.roomCode, 'dealComplete', {
                message: '发牌完成'
            });

            this.broadcastToRoom(room.roomCode, 'gameResults', results);

            console.log(`发牌完成: ${room.roomCode}`);

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理结束游戏（管理员）
     */
    handleEndGame(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            if (room.adminId !== socket.id) {
                throw new Error('只有管理员可以结束游戏');
            }

            // 获取游戏历史中的最新结果，而不是重新计算
            const gameHistory = room.gameManager.getGameHistory();
            let results = [];

            if (gameHistory.length > 0) {
                results = gameHistory[gameHistory.length - 1].results;
            }

            const gameEnd = room.endGame();

            // 广播游戏结果
            this.broadcastToRoom(room.roomCode, 'gameResults', results);
            this.broadcastToRoom(room.roomCode, 'gameEnd', gameEnd);

            console.log(`游戏结束: ${room.roomCode}`);

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理移除玩家（管理员）
     */
    handleRemovePlayer(socket, data) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            if (room.adminId !== socket.id) {
                throw new Error('只有管理员可以移除玩家');
            }

            const { playerId } = data;
            const result = this.roomManager.leaveRoom(playerId);

            if (result.success) {
                // 通知被移除的玩家
                const playerSocket = this.io.sockets.sockets.get(playerId);
                if (playerSocket) {
                    playerSocket.leave(room.roomCode);
                    playerSocket.emit('removedFromRoom', {
                        message: '您已被管理员移出房间'
                    });
                }

                // 广播玩家列表更新
                if (result.players) {
                    this.broadcastToRoom(room.roomCode, 'playersUpdate', result.players);
                }
            }

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理设置准备状态
     */
    handleSetReady(socket, data) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            const { ready } = data;
            room.setPlayerReady(socket.id, ready);

            // 广播玩家状态更新
            this.broadcastToRoom(room.roomCode, 'playersUpdate', room.getAllPlayers());

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理获取游戏状态
     */
    handleGetGameState(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            const gameState = room.gameManager.getGameState();
            const roomStatus = room.getRoomStatus();

            socket.emit('gameState', {
                room: roomStatus,
                game: gameState,
                players: room.getAllPlayers()
            });

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理获取游戏状态
     */
    handleGetGameState(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            socket.emit('gameState', {
                roomCode: room.roomCode,
                status: room.status,
                players: room.getAllPlayers(),
                canStart: room.canStartGame()
            });

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理获取玩家手牌
     */
    handleGetPlayerHand(socket) {
        try {
            const room = this.roomManager.getRoomByPlayer(socket.id);
            if (!room) {
                throw new Error('您不在任何房间中');
            }

            const hand = room.gameManager.getPlayerHand(socket.id);

            socket.emit('playerHand', hand);

        } catch (error) {
            socket.emit('gameError', {
                success: false,
                message: error.message
            });
        }
    }

    /**
     * 处理断线
     */
    handleDisconnect(socket) {
        console.log(`用户断线: ${socket.id}`);

        // 直接处理玩家断线，但不立即移除
        const result = this.roomManager.handlePlayerDisconnect(socket.id);

        if (result.success) {
            // 不再立即广播玩家离开消息，等待可能的重新连接
            console.log(`玩家断线处理完成: ${socket.id} in room ${result.roomCode}`);
        }
    }

    /**
     * 向房间广播消息
     */
    broadcastToRoom(roomCode, event, data) {
        this.io.to(roomCode).emit(event, data);
    }

    /**
     * 获取房间管理器统计信息
     */
    getStats() {
        return this.roomManager.getStats();
    }

    /**
     * 销毁处理器
     */
    destroy() {
        if (this.roomManager) {
            this.roomManager.destroy();
        }
    }
}

module.exports = SocketHandler;
