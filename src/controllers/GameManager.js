const Deck = require('../models/Deck');
const NiuniuCalculator = require('../utils/NiuniuCalculator');

/**
 * 游戏管理器类
 * 管理牛牛游戏的整个流程：发牌、计算、结果等
 */
class GameManager {
    constructor() {
        this.deck = new Deck();
        this.players = new Map(); // 玩家信息
        this.gameState = 'waiting'; // waiting, dealing, calculating, finished
        this.currentRound = 0;
        this.gameHistory = [];
    }
    
    /**
     * 添加玩家
     * @param {string} playerId 玩家ID
     * @param {string} playerName 玩家名称
     */
    addPlayer(playerId, playerName) {
        if (this.players.has(playerId)) {
            throw new Error('玩家已存在');
        }
        
        this.players.set(playerId, {
            id: playerId,
            name: playerName,
            hand: [],
            result: null,
            totalWins: 0,
            totalGames: 0
        });
        
        return this.getPlayerInfo(playerId);
    }
    
    /**
     * 移除玩家
     * @param {string} playerId 玩家ID
     */
    removePlayer(playerId) {
        if (!this.players.has(playerId)) {
            throw new Error('玩家不存在');
        }
        
        this.players.delete(playerId);
        return true;
    }
    
    /**
     * 获取玩家信息
     * @param {string} playerId 玩家ID
     */
    getPlayerInfo(playerId) {
        const player = this.players.get(playerId);
        if (!player) {
            throw new Error('玩家不存在');
        }
        
        return {
            id: player.id,
            name: player.name,
            handCount: player.hand.length,
            result: player.result,
            stats: {
                totalWins: player.totalWins,
                totalGames: player.totalGames,
                winRate: player.totalGames > 0 ? (player.totalWins / player.totalGames * 100).toFixed(1) : 0
            }
        };
    }
    
    /**
     * 获取所有玩家列表
     */
    getAllPlayers() {
        return Array.from(this.players.values()).map(player => ({
            id: player.id,
            name: player.name,
            handCount: player.hand.length,
            result: player.result
        }));
    }
    
    /**
     * 开始新游戏
     */
    startNewGame() {
        if (this.players.size < 2) {
            throw new Error('至少需要2个玩家才能开始游戏');
        }
        
        if (this.players.size > 8) {
            throw new Error('最多支持8个玩家');
        }
        
        // 重置牌库和玩家状态
        this.deck.reset();
        this.gameState = 'dealing';
        this.currentRound++;
        
        // 清空玩家手牌和结果
        for (const player of this.players.values()) {
            player.hand = [];
            player.result = null;
        }
        
        return {
            gameState: this.gameState,
            round: this.currentRound,
            playerCount: this.players.size
        };
    }
    
    /**
     * 发牌给所有玩家
     */
    dealCards() {
        if (this.gameState !== 'dealing') {
            throw new Error('当前游戏状态不允许发牌');
        }
        
        const playerIds = Array.from(this.players.keys());
        const playerHands = this.deck.dealToPlayers(playerIds.length, 5);
        
        // 分配手牌给玩家
        playerIds.forEach((playerId, index) => {
            const player = this.players.get(playerId);
            player.hand = playerHands[index];
        });
        
        this.gameState = 'calculating';
        
        return this.getAllPlayerHands();
    }
    
    /**
     * 获取所有玩家的手牌（用于管理员查看）
     */
    getAllPlayerHands() {
        const hands = {};
        
        for (const [playerId, player] of this.players) {
            hands[playerId] = {
                name: player.name,
                cards: player.hand.map(card => card.toJSON())
            };
        }
        
        return hands;
    }
    
    /**
     * 获取玩家的手牌
     * @param {string} playerId 玩家ID
     */
    getPlayerHand(playerId) {
        const player = this.players.get(playerId);
        if (!player) {
            throw new Error('玩家不存在');
        }
        
        return {
            cards: player.hand.map(card => card.toJSON()),
            result: player.result
        };
    }
    
    /**
     * 计算所有玩家的牛牛结果
     */
    calculateResults() {
        if (this.gameState !== 'calculating') {
            throw new Error('当前游戏状态不允许计算结果');
        }
        
        const results = [];
        
        // 计算每个玩家的结果
        for (const [playerId, player] of this.players) {
            if (player.hand.length !== 5) {
                throw new Error(`玩家 ${player.name} 的手牌不足5张`);
            }
            
            const result = NiuniuCalculator.calculateNiuniu(player.hand);
            player.result = result;
            
            results.push({
                playerId: playerId,
                playerName: player.name,
                ...result
            });
        }
        
        // 排序结果
        const sortedResults = NiuniuCalculator.sortResults(results);
        
        // 更新玩家统计
        this.updatePlayerStats(sortedResults);
        
        // 保存到历史记录
        this.gameHistory.push({
            round: this.currentRound,
            timestamp: new Date(),
            results: sortedResults,
            winner: sortedResults[0]
        });
        
        this.gameState = 'finished';
        
        return sortedResults;
    }
    
    /**
     * 更新玩家统计信息
     */
    updatePlayerStats(sortedResults) {
        // 获胜者是第一名
        const winnerId = sortedResults[0].playerId;
        
        for (const [playerId, player] of this.players) {
            player.totalGames++;
            if (playerId === winnerId) {
                player.totalWins++;
            }
        }
    }
    
    /**
     * 获取游戏状态
     */
    getGameState() {
        return {
            state: this.gameState,
            round: this.currentRound,
            playerCount: this.players.size,
            deckStatus: this.deck.getStatus()
        };
    }
    
    /**
     * 获取游戏历史
     */
    getGameHistory() {
        return this.gameHistory.map(game => ({
            round: game.round,
            timestamp: game.timestamp,
            winner: game.winner.playerName,
            winnerType: game.winner.type,
            winnerPoints: game.winner.points,
            playerCount: game.results.length
        }));
    }
    
    /**
     * 重置游戏
     */
    resetGame() {
        this.deck.reset();
        this.gameState = 'waiting';
        this.currentRound = 0;
        
        // 清空玩家手牌和结果，但保留统计信息
        for (const player of this.players.values()) {
            player.hand = [];
            player.result = null;
        }
        
        return this.getGameState();
    }
    
    /**
     * 清空所有数据
     */
    clearAll() {
        this.players.clear();
        this.gameHistory = [];
        this.resetGame();
        
        return this.getGameState();
    }
    
    /**
     * 验证游戏是否可以开始
     */
    canStartGame() {
        return this.players.size >= 2 && this.players.size <= 8 && this.gameState === 'waiting';
    }
    
    /**
     * 获取详细的游戏信息
     */
    getDetailedGameInfo() {
        return {
            gameState: this.getGameState(),
            players: this.getAllPlayers(),
            history: this.getGameHistory(),
            canStart: this.canStartGame()
        };
    }
}

module.exports = GameManager;
