const Room = require('../models/Room');

/**
 * 房间管理器类
 * 管理所有游戏房间的创建、查找、清理等操作
 */
class RoomManager {
    constructor() {
        this.rooms = new Map(); // roomCode -> Room
        this.playerRooms = new Map(); // socketId -> roomCode
        this.cleanupInterval = null;

        // 启动定期清理
        this.startCleanupTimer();
    }

    /**
     * 创建新房间
     * @param {string} adminId 管理员Socket ID
     * @param {string} adminName 管理员名称
     * @returns {Object} 房间信息
     */
    createRoom(adminId, adminName) {
        // 检查管理员是否已在其他房间
        if (this.playerRooms.has(adminId)) {
            throw new Error('您已在其他房间中');
        }

        // 生成唯一房间码
        let roomCode;
        let attempts = 0;
        do {
            roomCode = Room.generateRoomCode();
            attempts++;
            if (attempts > 100) {
                throw new Error('无法生成唯一房间码，请稍后重试');
            }
        } while (this.rooms.has(roomCode));

        // 创建房间
        const room = new Room(roomCode, adminId, adminName);

        // 管理员自动加入房间
        room.addPlayer(adminId, adminName);

        // 保存房间
        this.rooms.set(roomCode, room);
        this.playerRooms.set(adminId, roomCode);

        console.log(`房间创建成功: ${roomCode} (管理员: ${adminName})`);

        return {
            roomCode: roomCode,
            room: room.getRoomStatus(),
            message: '房间创建成功'
        };
    }

    /**
     * 加入房间
     * @param {string} roomCode 房间码
     * @param {string} socketId 玩家Socket ID
     * @param {string} playerName 玩家名称
     * @returns {Object} 加入结果
     */
    joinRoom(roomCode, socketId, playerName) {
        // 验证房间码格式
        if (!Room.isValidRoomCode(roomCode)) {
            throw new Error('房间码格式不正确');
        }

        // 检查房间是否存在
        const room = this.rooms.get(roomCode);
        if (!room) {
            throw new Error('房间不存在');
        }

        // 检查玩家是否已在其他房间
        if (this.playerRooms.has(socketId)) {
            const currentRoomCode = this.playerRooms.get(socketId);
            if (currentRoomCode !== roomCode) {
                throw new Error('您已在其他房间中');
            } else {
                throw new Error('您已在此房间中');
            }
        }

        // 加入房间
        const playerInfo = room.addPlayer(socketId, playerName);
        this.playerRooms.set(socketId, roomCode);

        console.log(`玩家加入房间: ${playerName} -> ${roomCode}`);

        return {
            roomCode: roomCode,
            playerInfo: playerInfo,
            room: room.getRoomStatus(),
            players: room.getAllPlayers(),
            message: '成功加入房间'
        };
    }

    /**
     * 离开房间
     * @param {string} socketId 玩家Socket ID
     * @returns {Object} 离开结果
     */
    leaveRoom(socketId) {
        const roomCode = this.playerRooms.get(socketId);
        if (!roomCode) {
            return { success: false, message: '您不在任何房间中' };
        }

        const room = this.rooms.get(roomCode);
        if (!room) {
            this.playerRooms.delete(socketId);
            return { success: false, message: '房间不存在' };
        }

        const player = room.getPlayer(socketId);
        const playerName = player ? player.name : 'Unknown';

        // 从房间移除玩家
        room.removePlayer(socketId);
        this.playerRooms.delete(socketId);

        console.log(`玩家离开房间: ${playerName} <- ${roomCode}`);

        // 如果房间为空，删除房间
        if (room.isEmpty()) {
            this.rooms.delete(roomCode);
            console.log(`房间已删除: ${roomCode}`);
            return {
                success: true,
                roomDeleted: true,
                message: '已离开房间，房间已删除'
            };
        }

        return {
            success: true,
            roomCode: roomCode,
            room: room.getRoomStatus(),
            players: room.getAllPlayers(),
            message: '已离开房间'
        };
    }

    /**
     * 获取房间信息
     * @param {string} roomCode 房间码
     * @returns {Room|null} 房间对象
     */
    getRoom(roomCode) {
        return this.rooms.get(roomCode) || null;
    }

    /**
     * 根据玩家ID获取房间
     * @param {string} socketId 玩家Socket ID
     * @returns {Room|null} 房间对象
     */
    getRoomByPlayer(socketId) {
        const roomCode = this.playerRooms.get(socketId);
        return roomCode ? this.rooms.get(roomCode) : null;
    }

    /**
     * 获取玩家所在房间码
     * @param {string} socketId 玩家Socket ID
     * @returns {string|null} 房间码
     */
    getPlayerRoomCode(socketId) {
        return this.playerRooms.get(socketId) || null;
    }

    /**
     * 检查玩家是否在房间中
     * @param {string} socketId 玩家Socket ID
     * @returns {boolean} 是否在房间中
     */
    isPlayerInRoom(socketId) {
        return this.playerRooms.has(socketId);
    }

    /**
     * 获取所有房间统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const totalRooms = this.rooms.size;
        const totalPlayers = this.playerRooms.size;

        let activeRooms = 0;
        let playingRooms = 0;
        let waitingRooms = 0;

        for (const room of this.rooms.values()) {
            if (room.players.size > 0) {
                activeRooms++;
                if (room.status === 'playing') {
                    playingRooms++;
                } else if (room.status === 'waiting') {
                    waitingRooms++;
                }
            }
        }

        return {
            totalRooms,
            activeRooms,
            playingRooms,
            waitingRooms,
            totalPlayers,
            averagePlayersPerRoom: totalRooms > 0 ? (totalPlayers / totalRooms).toFixed(1) : 0
        };
    }

    /**
     * 获取所有房间列表（用于管理）
     * @returns {Array} 房间列表
     */
    getAllRooms() {
        return Array.from(this.rooms.values()).map(room => ({
            roomCode: room.roomCode,
            adminName: room.adminName,
            playerCount: room.players.size,
            status: room.status,
            createdAt: room.createdAt,
            lastActivity: room.lastActivity
        }));
    }

    /**
     * 清理过期房间
     * @param {number} timeoutMinutes 超时时间（分钟）
     * @returns {number} 清理的房间数量
     */
    cleanupExpiredRooms(timeoutMinutes = 60) {
        let cleanedCount = 0;

        for (const [roomCode, room] of this.rooms) {
            if (room.isEmpty() || room.isExpired(timeoutMinutes)) {
                // 移除房间中的所有玩家记录
                for (const player of room.players.values()) {
                    this.playerRooms.delete(player.socketId);
                }

                // 删除房间
                this.rooms.delete(roomCode);
                cleanedCount++;

                console.log(`清理过期房间: ${roomCode}`);
            }
        }

        return cleanedCount;
    }

    /**
     * 启动定期清理定时器
     */
    startCleanupTimer() {
        // 每10分钟清理一次过期房间
        this.cleanupInterval = setInterval(() => {
            const cleaned = this.cleanupExpiredRooms(60); // 60分钟无活动的房间
            if (cleaned > 0) {
                console.log(`定期清理完成，清理了 ${cleaned} 个过期房间`);
            }
        }, 10 * 60 * 1000); // 10分钟
    }

    /**
     * 停止定期清理定时器
     */
    stopCleanupTimer() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }

    /**
     * 强制删除房间
     * @param {string} roomCode 房间码
     * @returns {boolean} 是否成功删除
     */
    forceDeleteRoom(roomCode) {
        const room = this.rooms.get(roomCode);
        if (!room) {
            return false;
        }

        // 移除所有玩家记录
        for (const player of room.players.values()) {
            this.playerRooms.delete(player.socketId);
        }

        // 删除房间
        this.rooms.delete(roomCode);

        console.log(`强制删除房间: ${roomCode}`);
        return true;
    }

    /**
     * 处理玩家断线
     * @param {string} socketId 断线的Socket ID
     * @returns {Object} 处理结果
     */
    handlePlayerDisconnect(socketId) {
        // 不立即移除玩家，而是标记为断线状态
        const roomCode = this.playerRooms.get(socketId);
        if (!roomCode) {
            return { success: false, message: '您不在任何房间中' };
        }

        const room = this.rooms.get(roomCode);
        if (!room) {
            this.playerRooms.delete(socketId);
            return { success: false, message: '房间不存在' };
        }

        // 标记玩家为断线状态而不是直接移除
        const player = room.getPlayer(socketId);
        if (player) {
            player.isDisconnected = true;
            player.disconnectedAt = new Date();
        }

        console.log(`玩家断线: ${socketId} in room ${roomCode}`);

        // 设置延迟清理任务
        setTimeout(() => {
            this.cleanupDisconnectedPlayer(socketId);
        }, 30000); // 30秒后清理断线玩家

        return {
            success: true,
            roomCode: roomCode,
            message: '玩家断线'
        };
    }

    /**
     * 清理断线玩家
     * @param {string} socketId 玩家Socket ID
     */
    cleanupDisconnectedPlayer(socketId) {
        const roomCode = this.playerRooms.get(socketId);
        if (!roomCode) {
            return;
        }

        const room = this.rooms.get(roomCode);
        if (!room) {
            this.playerRooms.delete(socketId);
            return;
        }

        // 检查玩家是否仍标记为断线状态
        const player = room.getPlayer(socketId);
        if (player && player.isDisconnected) {
            // 玩家仍然断线，执行真正的离开操作
            const playerName = player.name;

            // 从房间移除玩家
            room.removePlayer(socketId);
            this.playerRooms.delete(socketId);

            console.log(`清理断线玩家: ${playerName} <- ${roomCode}`);

            // 如果房间为空，删除房间
            if (room.isEmpty()) {
                this.rooms.delete(roomCode);
                console.log(`房间已删除: ${roomCode}`);
            } else {
                // 广播玩家列表更新
                this.broadcastToRoom(roomCode, 'playersUpdate', room.getAllPlayers());
                this.broadcastToRoom(roomCode, 'playerDisconnected', {
                    message: '有玩家断线离开'
                });
            }
        }
    }

    /**
     * 处理玩家重新连接
     * @param {string} oldSocketId 旧的Socket ID
     * @param {string} newSocketId 新的Socket ID
     * @param {string} playerName 玩家名称
     * @returns {Object} 处理结果
     */
    handlePlayerReconnect(oldSocketId, newSocketId, playerName) {
        const roomCode = this.playerRooms.get(oldSocketId);
        if (!roomCode) {
            return { success: false, message: '未找到玩家房间信息' };
        }

        const room = this.rooms.get(roomCode);
        if (!room) {
            this.playerRooms.delete(oldSocketId);
            return { success: false, message: '房间不存在' };
        }

        // 更新玩家的socket ID
        const player = room.getPlayer(oldSocketId);
        if (!player || player.name !== playerName) {
            return { success: false, message: '玩家信息不匹配' };
        }

        // 更新玩家信息
        player.socketId = newSocketId;
        player.isDisconnected = false;
        delete player.disconnectedAt;

        // 更新映射表
        this.playerRooms.delete(oldSocketId);
        this.playerRooms.set(newSocketId, roomCode);

        // 更新房间中的玩家ID
        room.players.delete(oldSocketId);
        room.players.set(newSocketId, player);
        room.playerOrder = room.playerOrder.map(id => id === oldSocketId ? newSocketId : id);

        // 如果是管理员，更新管理员ID
        if (room.adminId === oldSocketId) {
            room.adminId = newSocketId;
        }

        // 更新游戏管理器中的玩家信息
        try {
            room.gameManager.updatePlayerSocketId(oldSocketId, newSocketId);
        } catch (error) {
            console.warn('更新游戏管理器中的玩家Socket ID失败:', error.message);
        }

        console.log(`玩家重新连接: ${playerName} (${oldSocketId} -> ${newSocketId}) in room ${roomCode}`);

        return {
            success: true,
            roomCode: roomCode,
            room: room.getRoomStatus(),
            players: room.getAllPlayers(),
            message: '重新连接成功'
        };
    }

    /**
     * 广播消息到房间
     * @param {string} roomCode 房间码
     * @param {string} event 事件名
     * @param {Object} data 数据
     */
    broadcastToRoom(roomCode, event, data) {
        // 这里应该由SocketHandler提供实际的广播实现
        // 在RoomManager中我们只提供接口
    }

    /**
     * 获取房间管理器状态
     * @returns {Object} 状态信息
     */
    getManagerStatus() {
        return {
            stats: this.getStats(),
            rooms: this.getAllRooms(),
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage()
        };
    }

    /**
     * 销毁房间管理器
     */
    destroy() {
        this.stopCleanupTimer();
        this.rooms.clear();
        this.playerRooms.clear();
    }
}

module.exports = RoomManager;




